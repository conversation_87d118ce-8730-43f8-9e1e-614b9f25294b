<template>
  <div class="rack-detail-page">
    <!-- 顶部导航栏 -->
    <div class="page-header">
      <div class="header-left">
        <button
          class="back-btn"
          @click="goBack"
        >
          <svg
            class="back-icon"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
          >
            <path d="M19 12H5M12 19l-7-7 7-7" />
          </svg>
          返回机柜布局
        </button>
        <div class="breadcrumb">
          <span>机房管理</span>
          <span class="breadcrumb-separator">/</span>
          <span>机柜布局</span>
          <span class="breadcrumb-separator">/</span>
          <span class="breadcrumb-current">{{ rackId }}</span>
        </div>
      </div>

      <div class="header-center">
        <h1 class="page-title">
          <svg
            class="title-icon"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
          >
            <rect
              x="3"
              y="3"
              width="18"
              height="18"
              rx="2"
              ry="2"
            />
            <line
              x1="9"
              y1="9"
              x2="15"
              y2="9"
            />
            <line
              x1="9"
              y1="12"
              x2="15"
              y2="12"
            />
            <line
              x1="9"
              y1="15"
              x2="15"
              y2="15"
            />
          </svg>
          {{ rackId }} 详情
        </h1>
        <p class="page-subtitle">机柜设备管理与布局配置</p>
      </div>

      <div class="header-actions">
        <button
          class="action-btn primary"
          @click="onAddClick"
        >
          <svg
            class="btn-icon"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
          >
            <path d="M12 5v14M5 12h14" />
          </svg>
          新增设备
        </button>
        <button
          :disabled="!selectedId"
          class="action-btn secondary"
          @click="onEditClick"
        >
          <svg
            class="btn-icon"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
          >
            <path d="M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7" />
            <path d="M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z" />
          </svg>
          编辑
        </button>
        <button
          :disabled="!selectedId"
          class="action-btn danger"
          @click="onDeleteClick"
        >
          <svg
            class="btn-icon"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
          >
            <path d="M3 6h18M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2" />
          </svg>
          删除
        </button>
        <button
          class="action-btn secondary"
          @click="saveState"
        >
          <svg
            class="btn-icon"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
          >
            <path d="M19 21H5a2 2 0 01-2-2V5a2 2 0 012-2h11l5 5v11a2 2 0 01-2 2z" />
          </svg>
          保存布局
        </button>
      </div>
    </div>

    <!-- 机柜信息栏 -->
    <div class="rack-info-bar">
      <div class="info-item">
        <div class="info-label">机柜ID</div>
        <div class="info-value">{{ rackId }}</div>
      </div>
      <div class="info-item">
        <div class="info-label">总U位</div>
        <div class="info-value">42U</div>
      </div>
      <div class="info-item">
        <div class="info-label">已使用</div>
        <div class="info-value">{{ usedUnits }}U</div>
      </div>
      <div class="info-item">
        <div class="info-label">可用</div>
        <div class="info-value">{{ 42 - usedUnits }}U</div>
      </div>
      <div class="info-item">
        <div class="info-label">使用率</div>
        <div class="info-value">{{ Math.round((usedUnits / 42) * 100) }}%</div>
      </div>
      <div class="info-item">
        <div class="info-label">设备数量</div>
        <div class="info-value">{{ devices.length }}台</div>
      </div>
      <div class="info-item">
        <label class="toggle-label">
          <input
            v-model="showUIndex"
            type="checkbox"
            class="toggle-checkbox"
          />
          <span class="toggle-text">显示U位号</span>
        </label>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 机柜可视化容器 -->
      <div class="rack-visual-container">
        <div class="rack-visual-header">
          <h3 class="rack-title">{{ rackId }} 机柜可视化</h3>
          <div class="visual-controls">
            <label class="control-item">
              <input
                v-model="showUIndex"
                type="checkbox"
                class="control-checkbox"
              />
              <span class="control-text">显示U位号</span>
            </label>
          </div>
        </div>

        <!-- 机柜可视化主体容器 -->
        <div class="rack-visual-body">
            <h4 class="integrated-device-list-title">
              <svg
                class="integrated-device-list-icon"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
              >
                <path d="M9 12l2 2 4-4" />
                <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3" />
                <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3" />
                <path d="M13 12h3a2 2 0 012 2v1a2 2 0 01-2 2H6a2 2 0 01-2-2v-1a2 2 0 012-2h3" />
              </svg>
              设备列表
            </h4>
            <div class="integrated-device-list-container">
              <div
                v-for="device in devices"
                :key="device.id"
                class="integrated-device-item"
                :class="{ active: selectedId === device.id }"
                @click="selectDevice(device)"
              >
                <div class="integrated-device-icon">
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                  >
                    <rect
                      x="2"
                      y="3"
                      width="20"
                      height="14"
                      rx="2"
                      ry="2"
                    />
                    <line
                      x1="8"
                      y1="21"
                      x2="16"
                      y2="21"
                    />
                    <line
                      x1="12"
                      y1="17"
                      x2="12"
                      y2="21"
                    />
                  </svg>
                </div>
                <div class="integrated-device-info">
                  <div class="integrated-device-name">{{ device.name }}</div>
                  <div class="integrated-device-details">
                    <div class="integrated-device-grid">
                      <div class="integrated-device-row">
                        <span class="label">IP:</span>
                        <span class="value">{{ device.businessIp || device.host || '-' }}</span>
                      </div>
                      <div class="integrated-device-row">
                        <span class="label">负责人:</span>
                        <span class="value">{{ device.owner || '-' }}</span>
                      </div>
                      <div class="integrated-device-row">
                        <span class="label">SN:</span>
                        <span class="value">{{ device.serialNumber || '-' }}</span>
                      </div>
                      <div class="integrated-device-row">
                        <span class="label">U位:</span>
                        <span class="value">{{ device.uStart }}U-{{ device.uStart + device.uHeight - 1 }}U ({{ device.uHeight }}U)</span>
                      </div>
                      <div class="integrated-device-row">
                        <span class="label">状态:</span>
                        <span
                          class="value status"
                          :class="device.status"
                        >
                          {{ device.status === 'online' ? '在线' : '离线' }}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                v-if="devices.length === 0"
                class="no-devices"
              >
                暂无设备
              </div>
            </div>
          </div>

          <!-- 机柜容器 -->
          <div class="rack-container">
            <!-- PDU连线层 -->
            <svg
              class="pdu-connection-layer"
              viewBox="0 0 520 900"
              preserveAspectRatio="none"
            >
              <defs>
                <marker
                  id="arrowhead"
                  markerWidth="10"
                  markerHeight="7"
                  refX="9"
                  refY="3.5"
                  orient="auto"
                >
                  <polygon
                    points="0 0, 10 3.5, 0 7"
                    fill="#667eea"
                  />
                </marker>
              </defs>
              <!-- 连接线显示已移除 -->

              <!-- 临时拖拽连接线已移除 -->
            </svg>

            <!-- 左侧 PDU 列 -->
            <div class="pdu-column left-pdu">
              <div class="pdu-header">
                <svg
                  class="pdu-icon"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                >
                  <rect
                    x="3"
                    y="3"
                    width="18"
                    height="18"
                    rx="2"
                    ry="2"
                  />
                  <circle
                    cx="8.5"
                    cy="8.5"
                    r="1.5"
                  />
                  <circle
                    cx="15.5"
                    cy="8.5"
                    r="1.5"
                  />
                  <circle
                    cx="8.5"
                    cy="15.5"
                    r="1.5"
                  />
                  <circle
                    cx="15.5"
                    cy="15.5"
                    r="1.5"
                  />
                </svg>
                <div class="pdu-name">
                  <div class="pdu-name-line">市电</div>
                  <div class="pdu-name-line">PDU</div>
                </div>
              </div>
              <div class="pdu-body">
                <div
                  v-for="socket in 24"
                  :key="socket"
                  class="pdu-unit"
                  :class="{
                    connected: isPduSocketConnected('left', socket)
                  }"
                >
                  <div
                    class="pdu-outlet"
                    :class="{ active: isPduSocketConnected('left', socket) }"
                  ></div>
                  <div class="pdu-number">{{ socket }}</div>
                </div>
              </div>
            </div>

            <!-- 中间机柜主体 -->
            <div class="rack-main">
              <div class="rack-frame">
                <div class="rack-units">
                  <div
                    v-for="u in 42"
                    :key="u"
                    class="rack-unit"
                    :class="{
                      occupied: isOccupied(43 - u),
                      'drop-zone': true
                    }"
                    @drop="onDropToU(43 - u)"
                    @dragover.prevent
                    @dragenter.prevent
                  >
                    <!-- U 位号显示 -->
                    <div
                      v-if="showUIndex"
                      class="u-index"
                    >
                      {{ 43 - u }}
                    </div>

                    <!-- 设备卡片 -->
                    <div
                      v-for="dev in devicesAtU(43 - u)"
                      :key="dev.id"
                      class="device-card"
                      :class="{
                        selected: selectedId === dev.id,
                        [dev.os]: true
                      }"
                      :style="{ height: dev.uHeight * unitHeight + 'px' }"
                      draggable="true"
                      @dragstart="onDeviceDragStart(dev)"
                      @click="selectDevice(dev)"
                      @dblclick="onDeviceDblClick(dev)"
                    >
                      <!-- PDU连接点 -->
                      <div class="pdu-connection-points">
                        <!-- 左侧连接点 -->
                        <div
                          class="connection-point left-point"
                          :class="{
                            connected: dev.pduConnection?.type === 'left' || dev.pduConnection?.type === 'both'
                          }"
                          :title="
                            dev.pduConnection?.type === 'left' || dev.pduConnection?.type === 'both'
                              ? `已连接到左侧PDU插座${dev.pduConnection?.leftPduNumber}`
                              : '左侧PDU连接点'
                          "
                        ></div>

                        <!-- 右侧连接点 -->
                        <div
                          class="connection-point right-point"
                          :class="{
                            connected: dev.pduConnection?.type === 'right' || dev.pduConnection?.type === 'both'
                          }"
                          :title="
                            dev.pduConnection?.type === 'right' || dev.pduConnection?.type === 'both'
                              ? `已连接到右侧PDU插座${dev.pduConnection?.rightPduNumber}`
                              : '右侧PDU连接点'
                          "
                        ></div>
                      </div>
                      <div
                        class="device-content"
                        :class="{ compact: dev.uHeight === 1 }"
                      >
                        <!-- 1U设备简化显示 -->
                        <template v-if="dev.uHeight === 1">
                          <div class="device-simple">
                            <div class="device-name">{{ dev.name }}</div>
                            <div class="device-ip">{{ dev.host }}</div>
                          </div>
                        </template>

                        <!-- 多U设备简化显示 -->
                        <template v-else>
                          <div class="device-simple">
                            <div class="device-name">{{ dev.name }}</div>
                            <div class="device-ip">{{ dev.host }}</div>
                          </div>
                        </template>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 右侧 PDU 列 -->
            <div class="pdu-column right-pdu">
              <div class="pdu-header">
                <svg
                  class="pdu-icon"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                >
                  <rect
                    x="3"
                    y="3"
                    width="18"
                    height="18"
                    rx="2"
                    ry="2"
                  />
                  <circle
                    cx="8.5"
                    cy="8.5"
                    r="1.5"
                  />
                  <circle
                    cx="15.5"
                    cy="8.5"
                    r="1.5"
                  />
                  <circle
                    cx="8.5"
                    cy="15.5"
                    r="1.5"
                  />
                  <circle
                    cx="15.5"
                    cy="15.5"
                    r="1.5"
                  />
                </svg>
                <div class="pdu-name">
                  <div class="pdu-name-line">UPS</div>
                  <div class="pdu-name-line">PDU</div>
                </div>
              </div>
              <div class="pdu-body">
                <div
                  v-for="socket in 24"
                  :key="socket"
                  class="pdu-unit"
                  :class="{
                    connected: isPduSocketConnected('right', socket)
                  }"
                >
                  <div
                    class="pdu-outlet"
                    :class="{ active: isPduSocketConnected('right', socket) }"
                  ></div>
                  <div class="pdu-number">{{ socket }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧设备列表 -->
      <div class="device-list-container">
        <h4 class="device-list-title">
          <svg
            class="device-list-icon"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
          >
            <path d="M9 12l2 2 4-4" />
            <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3" />
            <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3" />
            <path d="M13 12h3a2 2 0 012 2v1a2 2 0 01-2 2H6a2 2 0 01-2-2v-1a2 2 0 012-2h3" />
          </svg>
          设备列表
        </h4>
        <div class="device-list-content">
          <div
            v-for="device in devices"
            :key="device.id"
            class="device-list-item"
            :class="{ active: selectedId === device.id }"
            @click="selectDevice(device)"
          >
            <div class="device-list-icon">
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
              >
                <rect x="2" y="3" width="20" height="14" rx="2" ry="2" />
                <line x1="8" y1="21" x2="16" y2="21" />
                <line x1="12" y1="17" x2="12" y2="21" />
              </svg>
            </div>
            <div class="device-list-info">
              <div class="device-list-name">{{ device.name }}</div>
              <div class="device-list-details">
                <div class="device-list-grid">
                  <div class="device-list-row">
                    <span class="label">IP:</span>
                    <span class="value">{{ device.businessIp || device.host || '-' }}</span>
                  </div>
                  <div class="device-list-row">
                    <span class="label">负责人:</span>
                    <span class="value">{{ device.owner || '-' }}</span>
                  </div>
                  <div class="device-list-row">
                    <span class="label">SN:</span>
                    <span class="value">{{ device.serialNumber || '-' }}</span>
                  </div>
                  <div class="device-list-row">
                    <span class="label">U位:</span>
                    <span class="value">{{ device.uStart }}U-{{ device.uStart + device.uHeight - 1 }}U ({{ device.uHeight }}U)</span>
                  </div>
                  <div class="device-list-row">
                    <span class="label">状态:</span>
                    <span class="value status" :class="device.status">
                      {{ device.status === 'online' ? '在线' : '离线' }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-if="devices.length === 0" class="no-devices">
            暂无设备
          </div>
        </div>
      </div>

      <!-- 右侧设备详情面板 -->
      <div class="device-detail-panel">
        <div
          v-if="selectedId"
          class="detail-panel-section"
        >
          <h4 class="detail-panel-title">
            <svg
              class="detail-panel-icon"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
            >
              <path d="M12 20h9" />
              <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z" />
            </svg>
            设备详情
          </h4>
          <div
            v-if="selectedDevice"
            class="expanded-device-details"
          >
            <div class="detail-item">
              <label>设备名称</label>
              <span>{{ selectedDevice.name }}</span>
            </div>
            <div class="detail-item">
              <label>IP地址</label>
              <span>{{ selectedDevice.host }}</span>
            </div>
            <div class="detail-item">
              <label>操作系统</label>
              <span>{{ selectedDevice.os }}</span>
            </div>
            <div class="detail-item">
              <label>U位位置</label>
              <span>{{ selectedDevice.uStart }}-{{ selectedDevice.uStart + selectedDevice.uHeight - 1 }}U</span>
            </div>
            <div class="detail-item">
              <label>设备高度</label>
              <span>{{ selectedDevice.uHeight }}U</span>
            </div>
            <div class="detail-item">
              <label>设备状态</label>
              <span class="status-online">在线</span>
            </div>
          </div>
        </div>
        <div
          v-else
          class="detail-panel-placeholder"
        >
          <div class="placeholder-content">
            <svg
              class="placeholder-icon"
              width="48"
              height="48"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
            >
              <path d="M9 12l2 2 4-4" />
              <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3" />
              <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3" />
              <path d="M13 12h3a2 2 0 012 2v1a2 2 0 01-2 2H6a2 2 0 01-2-2v-1a2 2 0 012-2h3" />
            </svg>
            <h4>选择设备查看详情</h4>
            <p>点击左侧设备列表中的任意设备，在此处查看详细信息</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 新增/编辑设备弹窗 -->
    <a-modal
      v-model:open="editModalVisible"
      :title="isEdit ? '编辑设备' : '新增设备'"
      ok-text="保存"
      cancel-text="取消"
      width="800px"
      @ok="onSubmitForm"
    >
      <div class="device-form">
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-title">
            <i class="icon-info"></i>
            基本信息
          </div>
          <div class="form-grid">
            <div class="form-group">
              <label class="form-label">设备名称 <span class="required">*</span></label>
              <a-input
                v-model:value="formState.name"
                placeholder="如：DB-01、WEB-SERVER-01"
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label class="form-label">资产编号</label>
              <a-input
                v-model:value="formState.assetNumber"
                placeholder="如：IT-2024-001"
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label class="form-label">设备类型 <span class="required">*</span></label>
              <a-select
                v-model:value="formState.deviceType"
                placeholder="请选择设备类型"
                class="form-input"
              >
                <a-select-option value="server">服务器</a-select-option>
                <a-select-option value="database">数据库服务器</a-select-option>
                <a-select-option value="network">网络设备</a-select-option>
                <a-select-option value="storage">存储设备</a-select-option>
                <a-select-option value="security">安全设备</a-select-option>
                <a-select-option value="other">其他设备</a-select-option>
              </a-select>
            </div>
            <div class="form-group">
              <label class="form-label">操作系统</label>
              <a-select
                v-model:value="formState.os"
                placeholder="请选择操作系统"
                class="form-input"
              >
                <a-select-option value="linux">Linux/Unix</a-select-option>
                <a-select-option value="windows">Windows Server</a-select-option>
                <a-select-option value="network">网络设备</a-select-option>
                <a-select-option value="vmware">VMware ESXi</a-select-option>
                <a-select-option value="other">其他</a-select-option>
              </a-select>
            </div>
          </div>
        </div>

        <!-- 硬件信息 -->
        <div class="form-section">
          <div class="section-title">
            <i class="icon-hardware"></i>
            硬件信息
          </div>
          <div class="form-grid">
            <div class="form-group">
              <label class="form-label">品牌</label>
              <a-select
                v-model:value="formState.brand"
                placeholder="请选择品牌"
                class="form-input brand-select"
                show-search
                allow-clear
                :get-popup-container="(triggerNode) => triggerNode.parentNode"
              >
                <a-select-option value="dell">Dell</a-select-option>
                <a-select-option value="hp">HP</a-select-option>
                <a-select-option value="lenovo">Lenovo</a-select-option>
                <a-select-option value="cisco">Cisco</a-select-option>
                <a-select-option value="huawei">华为</a-select-option>
                <a-select-option value="h3c">H3C</a-select-option>
                <a-select-option value="inspur">浪潮</a-select-option>
                <a-select-option value="other">其他</a-select-option>
              </a-select>
            </div>
            <div class="form-group">
              <label class="form-label">型号</label>
              <a-input
                v-model:value="formState.model"
                placeholder="如：PowerEdge R740"
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label class="form-label">序列号</label>
              <a-input
                v-model:value="formState.serialNumber"
                placeholder="设备序列号"
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label class="form-label">配置规格</label>
              <a-textarea
                v-model:value="formState.specifications"
                placeholder="如：CPU: Intel Xeon Gold 6248R, 内存: 64GB DDR4, 存储: 2TB SSD"
                class="form-input"
                :rows="2"
              />
            </div>
          </div>
        </div>

        <!-- 网络配置 -->
        <div class="form-section">
          <div class="section-title">
            <i class="icon-network"></i>
            网络配置
          </div>
          <div class="form-grid">
            <div class="form-group">
              <label class="form-label">管理IP <span class="required">*</span></label>
              <a-input
                v-model:value="formState.host"
                placeholder="************ 或域名"
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label class="form-label">管理端口</label>
              <a-input-number
                v-model:value="formState.port"
                :min="1"
                :max="65535"
                placeholder="端口号"
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label class="form-label">业务IP</label>
              <a-input
                v-model:value="formState.businessIp"
                placeholder="业务网络IP地址"
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label class="form-label">MAC地址</label>
              <a-input
                v-model:value="formState.macAddress"
                placeholder="如：00:1B:21:12:34:56"
                class="form-input"
              />
            </div>
          </div>
        </div>

        <!-- 认证信息 -->
        <div class="form-section">
          <div class="section-title">
            <i class="icon-auth"></i>
            认证信息
          </div>
          <div class="form-grid">
            <div class="form-group">
              <label class="form-label">用户名</label>
              <a-input
                v-model:value="formState.username"
                placeholder="登录用户名"
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label class="form-label">密码</label>
              <a-input-password
                v-model:value="formState.password"
                placeholder="登录密码"
                class="form-input"
              />
            </div>
          </div>
        </div>

        <!-- 机柜位置 -->
        <div class="form-section">
          <div class="section-title">
            <i class="icon-rack"></i>
            机柜位置
          </div>
          <div class="form-grid">
            <div class="form-group">
              <label class="form-label">U 起始位置 <span class="required">*</span></label>
              <a-input-number
                v-model:value="formState.uStart"
                :min="1"
                :max="42"
                placeholder="从底部开始计数"
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label class="form-label">U 高度 <span class="required">*</span></label>
              <a-input-number
                v-model:value="formState.uHeight"
                :min="1"
                :max="42"
                placeholder="占用U位数"
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label class="form-label">PDU 连接</label>
              <div
                class="pdu-connection-config"
                v-if="formState.pduConnection"
              >
                <a-select
                  v-model:value="formState.pduConnection.type"
                  placeholder="选择PDU连接方式"
                  class="form-input"
                  style="margin-bottom: 8px"
                >
                  <a-select-option value="none">无连接</a-select-option>
                  <a-select-option value="left">左侧PDU</a-select-option>
                  <a-select-option value="right">右侧PDU</a-select-option>
                  <a-select-option value="both">双PDU连接</a-select-option>
                </a-select>

                <div
                  v-if="formState.pduConnection && (formState.pduConnection.type === 'left' || formState.pduConnection.type === 'both')"
                  class="pdu-number-input"
                >
                  <label class="pdu-label">左侧PDU插座号:</label>
                  <a-input-number
                    v-model:value="formState.pduConnection.leftPduNumber"
                    :min="1"
                    :max="24"
                    placeholder="1-24"
                    style="width: 80px"
                  />
                </div>

                <div
                  v-if="formState.pduConnection && (formState.pduConnection.type === 'right' || formState.pduConnection.type === 'both')"
                  class="pdu-number-input"
                >
                  <label class="pdu-label">右侧PDU插座号:</label>
                  <a-input-number
                    v-model:value="formState.pduConnection.rightPduNumber"
                    :min="1"
                    :max="24"
                    placeholder="1-24"
                    style="width: 80px"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 管理信息 -->
        <div class="form-section">
          <div class="section-title">
            <i class="icon-manage"></i>
            管理信息
          </div>
          <div class="form-grid">
            <div class="form-group">
              <label class="form-label">所属部门</label>
              <a-select
                v-model:value="formState.department"
                placeholder="请选择部门"
                class="form-input"
              >
                <a-select-option value="技术部">技术部</a-select-option>
                <a-select-option value="运维部">运维部</a-select-option>
                <a-select-option value="网络部">网络部</a-select-option>
                <a-select-option value="数据部">数据部</a-select-option>
                <a-select-option value="安全部">安全部</a-select-option>
              </a-select>
            </div>
            <div class="form-group">
              <label class="form-label">负责人</label>
              <a-input
                v-model:value="formState.owner"
                placeholder="设备负责人"
                class="form-input"
              />
            </div>
            <div class="form-group">
              <label class="form-label">设备状态</label>
              <a-select
                v-model:value="formState.status"
                placeholder="请选择状态"
                class="form-input"
              >
                <a-select-option value="online">在线</a-select-option>
                <a-select-option value="offline">离线</a-select-option>
                <a-select-option value="maintenance">维护中</a-select-option>
                <a-select-option value="fault">故障</a-select-option>
                <a-select-option value="retired">已退役</a-select-option>
              </a-select>
            </div>
            <div class="form-group">
              <label class="form-label">购买日期</label>
              <a-date-picker
                v-model:value="formState.purchaseDate"
                placeholder="选择购买日期"
                class="form-input"
                style="width: 100%"
              />
            </div>
            <div class="form-group full-width">
              <label class="form-label">备注</label>
              <a-textarea
                v-model:value="formState.description"
                placeholder="设备备注信息"
                class="form-input"
                :rows="3"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="form-hint">
        <i class="icon-warning"></i>
        注意：同一 U 段不能重叠，占用必须在 1-42U 范围内。
      </div>
    </a-modal>
  </div>
</template>
<script setup lang="ts">
/**
 * 文件: RackDetail.vue
 * 功能: 42U 机柜可视化、设备占用与拖拽移动；双击设备复用已有 SSH/RDP 连接逻辑。
 * 依赖: Vue 3, vue-router, 全局 eventBus, electronAPI（RDP）, Ant Design Vue
 * 作者: AI 助手
 * 修改时间: 自动生成
 */
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'

// 全局拖拽状态管理
interface GlobalDragState {
  device: RackDevice | null
  sourceRackId: string | null
  isActive: boolean
}

// 创建全局拖拽状态
const globalDragState = ref<GlobalDragState>({
  device: null,
  sourceRackId: null,
  isActive: false
})

// 将全局状态挂载到 window 对象，供其他机柜页面访问
if (typeof window !== 'undefined') {
  ;(window as any).rackDragState = globalDragState
}
import eventBus from '../../utils/eventBus'

interface RackDevice {
  id: string
  name: string
  assetNumber?: string // 资产编号
  deviceType?: string // 设备类型
  os: 'linux' | 'windows' | 'network' | 'vmware' | 'other'
  host: string
  port: number
  businessIp?: string // 业务IP
  macAddress?: string // MAC地址
  username?: string
  password?: string
  brand?: string // 品牌
  model?: string // 型号
  serialNumber?: string // 序列号
  specifications?: string // 配置规格
  department?: string // 所属部门
  owner?: string // 负责人
  status?: string // 设备状态
  purchaseDate?: string // 购买日期
  description?: string // 备注
  uStart: number // 1..42 从底部计数，1U在底部，42U在顶部
  uHeight: number // 占用U数
  pduConnection?: {
    type: 'left' | 'right' | 'both' | 'none'
    leftPduNumber?: number // 左侧PDU具体数字 (1-24)
    rightPduNumber?: number // 右侧PDU具体数字 (1-24)
  } // PDU连接方式
}

const route = useRoute()
const router = useRouter()

const roomId = String(route.params.roomId || '')
const rackId = String(route.params.rackId || '')
const storageKey = `rack_detail_${rackId}`

const unitHeight = 25 // px per U，用于可视化
const showUIndex = ref(true)
const devices = ref<RackDevice[]>([])

// 计算已使用的U位数量
const usedUnits = computed(() => {
  return devices.value.reduce((total, device) => total + device.uHeight, 0)
})

// 获取当前选中的设备
const selectedDevice = computed(() => {
  return selectedId.value ? devices.value.find((d) => d.id === selectedId.value) : null
})

// PDU连接线计算函数已移除

let draggingDevice: RackDevice | null = null

// PDU连接线拖拽状态已移除

// 连线选择状态已移除

// 设备选择与表单状态
const selectedId = ref<string | null>(null)
const editModalVisible = ref(false)
const isEdit = ref(false)
const formState = ref<RackDevice>({
  id: '',
  name: '',
  assetNumber: '',
  deviceType: '',
  os: 'linux',
  host: '',
  port: 22,
  businessIp: '',
  macAddress: '',
  username: '',
  password: '',
  brand: '',
  model: '',
  serialNumber: '',
  specifications: '',
  department: '',
  owner: '',
  status: 'online',
  purchaseDate: '',
  description: '',
  uStart: 1,
  uHeight: 1,
  pduConnection: { type: 'none', leftPduNumber: undefined, rightPduNumber: undefined }
})

function loadState() {
  try {
    const raw = localStorage.getItem(storageKey)
    const loadedDevices = raw ? JSON.parse(raw) : []

    // 数据验证和清理
    devices.value = loadedDevices.filter((device: any) => {
      // 检查必要字段
      if (!device.id || !device.name || typeof device.uHeight !== 'number' || typeof device.uStart !== 'number') {
        console.warn('发现无效设备数据，已过滤:', device)
        return false
      }

      // 检查uHeight是否合理（1-42之间）
      if (device.uHeight < 1 || device.uHeight > 42) {
        console.warn('发现异常uHeight值，已过滤:', device)
        return false
      }

      // 检查uStart是否合理（1-42之间）
      if (device.uStart < 1 || device.uStart > 42) {
        console.warn('发现异常uStart值，已过滤:', device)
        return false
      }

      return true
    })

    console.log('加载的设备数据:', devices.value)
  } catch (e) {
    console.error('读取机柜设备失败:', e)
    devices.value = []
  }
}

function saveState() {
  try {
    localStorage.setItem(storageKey, JSON.stringify(devices.value))
    console.log('已保存设备数据:', devices.value)
  } catch (e) {
    console.error('保存机柜设备失败:', e)
  }
}

// 清理localStorage中的异常数据
function cleanupStorage() {
  try {
    const allKeys = Object.keys(localStorage)
    const rackKeys = allKeys.filter((key) => key.startsWith('rack_detail_'))

    rackKeys.forEach((key) => {
      try {
        const data = JSON.parse(localStorage.getItem(key) || '[]')
        const cleanData = data.filter((device: any) => {
          return (
            device &&
            typeof device.uHeight === 'number' &&
            typeof device.uStart === 'number' &&
            device.uHeight >= 1 &&
            device.uHeight <= 42 &&
            device.uStart >= 1 &&
            device.uStart <= 42
          )
        })

        if (cleanData.length !== data.length) {
          console.log(`清理了 ${key} 中的异常数据，原有 ${data.length} 个设备，清理后 ${cleanData.length} 个设备`)
          localStorage.setItem(key, JSON.stringify(cleanData))
        }
      } catch (e) {
        console.warn(`清理 ${key} 时出错:`, e)
        localStorage.removeItem(key)
      }
    })
  } catch (e) {
    console.error('清理localStorage失败:', e)
  }
}

// 组件挂载时初始化
onMounted(() => {
  // 先清理可能的异常数据
  cleanupStorage()
  loadState()

  // 确保机柜视图滚动到顶部，显示42U位开始
  nextTick(() => {
    const rackUnitsContainer = document.querySelector('.rack-units')
    if (rackUnitsContainer) {
      rackUnitsContainer.scrollTop = 0
    }
  })

  // 监听跨机柜设备移动事件
  eventBus.on('device-moved-to-other-rack', (data: any) => {
    if (data.sourceRackId === rackId) {
      // 从当前机柜移除被拖拽到其他机柜的设备
      devices.value = devices.value.filter((d) => d.id !== data.deviceId)
      saveState()

      // 如果被移除的设备正在被选中，清除选中状态
      if (selectedId.value === data.deviceId) {
        selectedId.value = null
      }
    }
  })

  // 监听拖拽结束事件，清理本地拖拽状态
  eventBus.on('device-drag-end', () => {
    draggingDevice = null
  })

  // 从全局状态恢复拖拽状态（页面刷新后）
  if (typeof window !== 'undefined' && (window as any).rackDragState) {
    const globalState = (window as any).rackDragState
    if (globalState.value && globalState.value.isActive) {
      globalDragState.value = globalState.value
    }
  }
})

function isOccupied(u: number) {
  return devices.value.some((d) => u >= d.uStart && u < d.uStart + d.uHeight)
}

function devicesAtU(u: number) {
  // 仅在设备起始U渲染设备卡片，避免同一设备渲染多次
  return devices.value.filter((d) => u === d.uStart)
}

function onDeviceDragStart(dev: RackDevice) {
  draggingDevice = dev
  // 设置全局拖拽状态，支持跨机柜拖拽
  globalDragState.value = {
    device: { ...dev },
    sourceRackId: rackId,
    isActive: true
  }

  // 通知其他机柜页面有设备正在拖拽
  eventBus.emit('device-drag-start', {
    device: dev,
    sourceRackId: rackId
  })
}

function rangeConflict(targetU: number, height: number, ignoreId?: string) {
  // 检查目标区域是否被其他设备占用，避免重叠
  return devices.value.some((d) => d.id !== ignoreId && targetU < d.uStart + d.uHeight && targetU + height > d.uStart)
}

function onDropToU(targetU: number) {
  // 检查是否有本地拖拽的设备
  if (draggingDevice) {
    // 本机柜内拖拽
    if (targetU + draggingDevice.uHeight - 1 > 42) return
    const conflict = rangeConflict(targetU, draggingDevice.uHeight, draggingDevice.id)
    if (conflict) return
    draggingDevice.uStart = targetU
    saveState()
    draggingDevice = null
    clearGlobalDragState()
    return
  }

  // 检查是否有跨机柜拖拽的设备
  if (globalDragState.value.isActive && globalDragState.value.device) {
    const draggedDevice = globalDragState.value.device
    const sourceRackId = globalDragState.value.sourceRackId

    // 检查目标位置是否可用
    if (targetU + draggedDevice.uHeight - 1 > 42) {
      alert('目标位置超出机柜范围')
      return
    }

    const conflict = rangeConflict(targetU, draggedDevice.uHeight)
    if (conflict) {
      alert('目标位置与现有设备冲突')
      return
    }

    // 创建新设备并添加到当前机柜
    const newDevice: RackDevice = {
      ...draggedDevice,
      id: Date.now().toString(), // 生成新ID
      uStart: targetU
    }

    devices.value.push(newDevice)
    saveState()

    // 通知源机柜移除设备
    if (sourceRackId && sourceRackId !== rackId) {
      eventBus.emit('device-moved-to-other-rack', {
        deviceId: draggedDevice.id,
        sourceRackId: sourceRackId,
        targetRackId: rackId
      })
    }

    clearGlobalDragState()
  }
}

// 清理全局拖拽状态
function clearGlobalDragState() {
  globalDragState.value = {
    device: null,
    sourceRackId: null,
    isActive: false
  }

  eventBus.emit('device-drag-end')
}

function selectDevice(dev: RackDevice) {
  selectedId.value = dev.id
}

function findFirstFit(height: number) {
  for (let u = 1; u <= 42 - height + 1; u++) {
    if (!rangeConflict(u, height)) return u
  }
  return 1
}

function onAddClick() {
  isEdit.value = false
  const defaultHeight = 1
  const start = findFirstFit(defaultHeight)
  formState.value = {
    id: Date.now().toString(),
    name: '新设备',
    assetNumber: '',
    deviceType: '',
    os: 'linux',
    host: '************',
    port: 22,
    businessIp: '',
    macAddress: '',
    username: '',
    password: '',
    brand: '',
    model: '',
    serialNumber: '',
    specifications: '',
    department: '',
    owner: '',
    status: 'online',
    purchaseDate: '',
    description: '',
    uStart: start,
    uHeight: defaultHeight,
    pduConnection: { type: 'none', leftPduNumber: undefined, rightPduNumber: undefined }
  }
  editModalVisible.value = true
}

function onEditClick() {
  if (!selectedId.value) return
  const dev = devices.value.find((d) => d.id === selectedId.value)
  if (!dev) return
  isEdit.value = true
  formState.value = {
    ...dev,
    pduConnection: dev.pduConnection || { type: 'none', leftPduNumber: undefined, rightPduNumber: undefined }
  }
  editModalVisible.value = true
}

function onDeleteClick() {
  if (!selectedId.value) return
  const dev = devices.value.find((d) => d.id === selectedId.value)
  if (!dev) return
  if (window.confirm(`确认删除设备“${dev.name}”？`)) {
    devices.value = devices.value.filter((d) => d.id !== dev.id)
    selectedId.value = null
    saveState()
  }
}

// PDU连接文本函数已移除

function onSubmitForm() {
  const f = formState.value
  // 合法性校验
  if (!f.name.trim()) return alert('名称不能为空')
  if (!f.host.trim()) return alert('主机不能为空')
  if (f.uStart < 1 || f.uStart > 42) return alert('U 起始必须在 1-42 范围内')
  if (f.uHeight < 1 || f.uHeight > 42) return alert('U 高度必须在 1-42 范围内')
  if (f.uStart + f.uHeight - 1 > 42) return alert('U 段越界：请调整 U 起始或高度')

  const conflict = rangeConflict(f.uStart, f.uHeight, isEdit.value ? f.id : undefined)
  if (conflict) return alert('目标 U 段与其他设备重叠，请调整')

  if (isEdit.value) {
    const idx = devices.value.findIndex((d) => d.id === f.id)
    if (idx !== -1) devices.value[idx] = { ...f }
  } else {
    // 根据操作系统默认端口
    if (f.os === 'windows' && (!f.port || f.port === 22)) f.port = 3389
    if (f.os !== 'windows' && (!f.port || f.port === 3389)) f.port = 22
    devices.value.push({ ...f })
  }

  saveState()
  editModalVisible.value = false
}

function onDeviceDblClick(dev: RackDevice) {
  if (dev.os === 'windows') {
    // 复用现有 RDP 打开逻辑
    const rdpConfig = {
      assetId: dev.id,
      type: 'rdp',
      config: { host: dev.host, port: dev.port, username: dev.username, password: dev.password }
    }
    // @ts-ignore
    window.electronAPI
      ?.rdpConnect(rdpConfig)
      .then((response: any) => {
        if (response?.success) {
          eventBus.emit('openRdpWindow', {
            connectionId: response.connectionId,
            assetName: dev.name,
            assetId: dev.id
          })
        } else {
          console.warn('RDP 连接失败', response?.message)
        }
      })
      .catch((e: any) => console.error('RDP 连接异常', e))
  } else {
    // 复用 SSH 逻辑：发射 currentClickServer
    const assetNode = {
      uuid: dev.id,
      label: dev.name,
      ip: dev.host,
      port: dev.port,
      username: dev.username || '',
      password: dev.password || '',
      group_name: '机柜资产',
      asset_type: 'organization',
      connection_type: 'ssh',
      key_chain_id: null,
      need_proxy: false,
      proxy_name: '',
      description: `${rackId}`
    }
    eventBus.emit('currentClickServer', assetNode)
  }
}

function goBack() {
  router.back()
}

// PDU连接线相关辅助函数已移除

/**
 * 检查PDU插座是否有设备连接（保留用于显示连接状态）
 */
function isPduSocketConnected(side: 'left' | 'right', socketNumber: number): boolean {
  return devices.value.some((device) => {
    if (!device.pduConnection || device.pduConnection.type === 'none') return false

    if (side === 'left') {
      return (device.pduConnection.type === 'left' || device.pduConnection.type === 'both') && device.pduConnection.leftPduNumber === socketNumber
    } else {
      return (device.pduConnection.type === 'right' || device.pduConnection.type === 'both') && device.pduConnection.rightPduNumber === socketNumber
    }
  })
}

// startConnectionDrag函数已移除

// endConnectionDrag函数已移除

// connectToPduSocket函数已移除

// PDU插座悬停相关函数已移除

// 连接线选择和删除函数已移除

// 设备状态CSS类函数已移除

/**
 * 获取设备状态文本
 */
function getDeviceStatusText(status: string): string {
  switch (status) {
    case 'online':
      return '在线'
    case 'offline':
      return '离线'
    case 'maintenance':
      return '维护'
    case 'fault':
      return '故障'
    default:
      return '在线'
  }
}

/**
 * 获取设备类型文本
 */
function getDeviceTypeText(deviceType: string): string {
  switch (deviceType) {
    case 'server':
      return '服务器'
    case 'database':
      return '数据库服务器'
    case 'network':
      return '网络设备'
    case 'storage':
      return '存储设备'
    case 'security':
      return '安全设备'
    case 'other':
      return '其他设备'
    default:
      return '未知设备'
  }
}
</script>
<style scoped>
/* 页面整体布局 */
.rack-detail-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 顶部导航栏 */
.page-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  padding: 20px 24px;
  position: sticky;
  top: 0;
  z-index: 2100;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  /* 启用窗口拖拽 */
  -webkit-app-region: drag;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: #fff;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #475569;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  /* 禁用拖拽以保持按钮功能 */
  -webkit-app-region: no-drag;
}

.back-btn:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.back-icon {
  width: 16px;
  height: 16px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #64748b;
  font-size: 14px;
}

.breadcrumb-separator {
  color: #cbd5e1;
}

.breadcrumb-current {
  color: #3b82f6;
  font-weight: 500;
}

.header-center {
  text-align: center;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  justify-content: center;
}

.title-icon {
  color: #3b82f6;
}

.page-subtitle {
  color: #64748b;
  font-size: 14px;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
  /* 禁用拖拽以保持按钮功能 */
  -webkit-app-region: no-drag;
}

/* 机柜信息栏 */
.rack-info-bar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  margin: 16px 24px;
  border-radius: 16px;
  padding: 24px;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(226, 232, 240, 0.8);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.info-item {
  text-align: center;
}

.info-label {
  display: block;
  font-size: 14px;
  color: #64748b;
  margin-bottom: 8px;
  font-weight: 500;
}

.info-value {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  user-select: none;
}

.toggle-checkbox {
  width: 16px;
  height: 16px;
  accent-color: #667eea;
}

.toggle-text {
  font-size: 0.9rem;
  color: #4a5568;
}

/* 主要内容区域 */
.main-content {
  display: flex;
  gap: 48px; /* 增加间距从24px到48px */
  padding: 0 24px 32px;
  max-width: 1800px; /* 进一步增加最大宽度 */
  margin: 0 auto;
  justify-content: flex-start; /* 内容左对齐 */
  align-items: flex-start;
}

/* 机柜内容水平布局容器 */
.rack-content-layout {
  display: flex;
  gap: 20px; /* 减少间距，让布局更紧凑 */
  width: 100%;
  align-items: flex-start;
}

/* 机柜可视化容器 */
.rack-visual-container {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 32px; /* 增加内边距 */
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.08),
    0 1px 4px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(226, 232, 240, 0.8);
  max-height: 85vh;
  overflow-y: auto;
  flex: 2; /* 增加flex比例，让机柜容器占更多空间 */
  min-width: 1200px; /* 进一步增加机柜宽度 */
  display: flex;
  flex-direction: column;
}

.rack-visual-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  width: 100%; /* 确保头部占满容器宽度 */
}

.rack-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
}

.visual-controls {
  display: flex;
  gap: 1rem;
}

.control-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

/* 机柜可视化主体容器 */
.rack-visual-body {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;
  /* 移除 overflow: hidden，允许父容器滚动 */
}

/* 设备列表容器样式 */
.device-list-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 24px;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.08),
    0 1px 4px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(226, 232, 240, 0.8);
  max-height: 85vh;
  overflow-y: auto;
  flex: 1; /* 占据剩余空间 */
  min-width: 350px; /* 设置最小宽度 */
  display: flex;
  flex-direction: column;
}

.device-list-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0 1.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid rgba(226, 232, 240, 0.8);
}

.device-list-icon {
  color: #667eea;
  width: 20px;
  height: 20px;
}

.device-list-content {
  flex: 1;
  overflow-y: auto;
  padding-right: 4px; /* 为滚动条留出空间 */
}

/* 自定义滚动条样式 */
.device-list-content::-webkit-scrollbar {
  width: 6px;
}

.device-list-content::-webkit-scrollbar-track {
  background: rgba(226, 232, 240, 0.3);
  border-radius: 3px;
}

.device-list-content::-webkit-scrollbar-thumb {
  background: rgba(103, 126, 234, 0.4);
  border-radius: 3px;
}

.device-list-content::-webkit-scrollbar-thumb:hover {
  background: rgba(103, 126, 234, 0.6);
}

.device-list-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 8px;
  border: 1px solid transparent;
  background: rgba(248, 250, 252, 0.8);
}

.device-list-item:hover {
  background: rgba(103, 126, 234, 0.1);
  border-color: rgba(103, 126, 234, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.device-list-item.active {
  background: rgba(103, 126, 234, 0.15);
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(103, 126, 234, 0.2);
}

.device-list-icon {
  color: #667eea;
  flex-shrink: 0;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 2px;
}

.device-list-info {
  flex: 1;
  min-width: 0;
}

.device-list-name {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 6px;
  font-size: 0.95rem;
  line-height: 1.3;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.device-list-details {
  font-size: 0.8rem;
  color: #64748b;
  line-height: 1.3;
}

.device-list-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: repeat(3, auto);
  gap: 4px 8px;
  grid-template-areas:
    'ip owner'
    'sn status'
    'upos upos';
}

.device-list-grid .device-list-row:nth-child(1) {
  grid-area: ip;
}

.device-list-grid .device-list-row:nth-child(2) {
  grid-area: owner;
}

.device-list-grid .device-list-row:nth-child(3) {
  grid-area: sn;
}

.device-list-grid .device-list-row:nth-child(4) {
  grid-area: upos;
}

.device-list-grid .device-list-row:nth-child(5) {
  grid-area: status;
}

.device-list-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 6px;
}

.device-list-row .label {
  font-weight: 500;
  color: #475569;
  flex-shrink: 0;
  min-width: 40px;
}

.device-list-row .value {
  color: #64748b;
  text-align: right;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.device-list-row .value.status {
  font-weight: 500;
}

.device-list-row .value.status.online {
  color: #10b981;
}

.device-list-row .value.status.offline {
  color: #ef4444;
}

.no-devices {
  text-align: center;
  color: #94a3b8;
  font-size: 0.9rem;
  padding: 2rem;
  font-style: italic;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-left: 0.5rem;
}

.status-dot.online {
  background-color: #48bb78;
  box-shadow: 0 0 0 2px rgba(72, 187, 120, 0.3);
}

.status-dot.offline {
  background-color: #f56565;
  box-shadow: 0 0 0 2px rgba(245, 101, 101, 0.3);
}

.control-item {
  user-select: none;
}

.control-checkbox {
  width: 16px;
  height: 16px;
  accent-color: #667eea;
}

.control-text {
  font-size: 0.9rem;
  color: #4a5568;
}

/* 机柜容器 */
.rack-container {
  display: flex;
  gap: 2rem; /* 增加PDU和机柜之间的间距 */
  justify-content: center; /* 居中显示，在可用空间内均匀分布 */
  align-items: flex-start;
  position: relative;
  padding: 0; /* 移除内边距 */
  margin: 0; /* 移除边距限制 */
  width: 100%; /* 使用全部可用宽度 */
}

/* PDU连接层样式已移除 */

/* 连接线样式已移除 */

/* PDU 列 */
.pdu-column {
  width: 150px; /* 进一步增加宽度到150px */
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.pdu-header {
  background: linear-gradient(135deg, #4a5568, #2d3748);
  color: white;
  padding: 1rem 0.75rem; /* 增加内边距 */
  text-align: center;
  font-size: 0.8rem; /* 稍微增加字体大小 */
  font-weight: 600;
  display: flex;
  flex-direction: column; /* 改为垂直布局以支持两行显示 */
  align-items: center;
  justify-content: center;
  gap: 0.5rem; /* 增加图标和文字之间的间距 */
  min-height: 80px; /* 设置最小高度以容纳两行文字 */
}

.pdu-icon {
  width: 20px; /* 增加图标大小 */
  height: 20px;
}

.pdu-name {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.1rem;
}

.pdu-name-line {
  font-size: 0.8rem;
  font-weight: 600;
  line-height: 1;
  text-align: center;
}

.pdu-body {
  max-height: 1680px; /* 增加到1680px以适应更大的PDU单元 (42U × 40px) */
  overflow-y: auto;
}

.pdu-unit {
  height: 40px; /* 匹配机柜单元的高度 */
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  padding: 0 10px; /* 增加内边距 */
}

.pdu-outlet {
  width: 8px;
  height: 8px;
  background: #cbd5e0;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.pdu-outlet.active {
  background: #10b981;
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.6);
  transform: scale(1.2);
}

.pdu-number {
  font-size: 10px;
  color: #64748b;
  font-weight: 500;
}

.pdu-unit.connected .pdu-number {
  color: #10b981;
  font-weight: 600;
}

.pdu-unit:hover .pdu-outlet {
  background: #667eea;
  transform: scale(1.2);
}

.pdu-unit.connected:hover .pdu-outlet {
  background: #059669;
}

/* 机柜主体 */
.rack-main {
  width: 650px; /* 进一步增加宽度到650px */
  background: #2d3748;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.rack-frame {
  background: linear-gradient(180deg, #4a5568 0%, #2d3748 100%);
  padding: 1rem;
}

.rack-units {
  background: #1a202c;
  border-radius: 8px;
  padding: 0.75rem; /* 增加内边距 */
  height: 1680px; /* 进一步增加高度 (42U × 40px) */
  overflow-y: auto;
  display: flex;
  flex-direction: column; /* 改回正常排列，通过U位号计算来显示正确的顺序 */
  scroll-behavior: smooth;
}

.rack-unit {
  height: 40px; /* 进一步增加高度到40px */
  border: 1px solid #4a5568;
  margin-bottom: 1px;
  position: relative;
  display: flex;
  align-items: center;
  background: #2d3748;
  transition: all 0.3s ease;
}

.rack-unit.drop-zone:hover {
  background: rgba(103, 126, 234, 0.2);
  border-color: #667eea;
}

.rack-unit.occupied {
  background: transparent;
  border-color: transparent;
}

.u-index {
  position: absolute;
  left: 4px;
  font-size: 0.7rem;
  color: #a0aec0;
  font-weight: 500;
  z-index: 2;
}

/* 设备卡片 */
.device-card {
  position: absolute;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 6px;
  margin: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  min-height: 48px; /* 从40px增加到48px以匹配更大的单元 */
}

.device-card:hover {
  transform: translateX(4px) scale(1.02);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.device-card.selected {
  background: linear-gradient(135deg, #48bb78, #38a169);
  box-shadow:
    0 0 0 2px #ffd700,
    0 4px 16px rgba(0, 0, 0, 0.3);
}

.device-card.linux {
  background: linear-gradient(135deg, #ed8936, #dd6b20);
}

.device-card.windows {
  background: linear-gradient(135deg, #4299e1, #3182ce);
}

.device-content {
  padding: 0.4rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  text-align: center;
}

.device-content.compact {
  padding: 0.2rem 0.4rem;
  justify-content: center;
  align-items: center;
}

/* 1U设备全新紧凑布局样式 */
.device-compact-new {
  display: flex;
  width: 100%;
  height: 100%;
  padding: 0.15rem 0.3rem;
  gap: 0.2rem;
  align-items: center;
  overflow: hidden;
  box-sizing: border-box;
}

.device-primary-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 0;
  gap: 0.05rem;
  overflow: hidden;
}

.device-name-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.2rem;
  min-height: 0;
}

.device-name-text {
  font-size: 0.7rem;
  font-weight: 700;
  line-height: 1;
  color: #ffffff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  min-width: 0;
  max-width: calc(100% - 60px);
}

/* 设备指示器样式已移除 */

/* 设备连接和次要信息样式已移除 */

/* 设备元信息样式已移除 */

/* PDU连接点样式已移除 */

/* 连线选择和删除样式已移除 */

/* 删除连接按钮和动画样式已移除 */

/* 临时连接线样式已移除 */

/* 修复品牌选择框重叠问题 */
.brand-select {
  position: relative !important;
  z-index: 1 !important;
}

.brand-select .ant-select-selector {
  background-color: #ffffff !important;
  border: 1px solid #d9d9d9 !important;
  border-radius: 6px !important;
  position: relative !important;
  z-index: 2 !important;
  color: #333333 !important;
}

.brand-select .ant-select-selection-item {
  color: #333333 !important;
  font-weight: 500 !important;
}

.brand-select .ant-select-selection-placeholder {
  color: #999999 !important;
}

.brand-select .ant-select-selector:hover {
  border-color: #4a90e2 !important;
}

.brand-select .ant-select-selector:focus,
.brand-select.ant-select-focused .ant-select-selector {
  border-color: #4a90e2 !important;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1) !important;
}

/* 确保下拉菜单正确显示 */
.brand-select .ant-select-dropdown {
  background-color: #ffffff !important;
  border: 1px solid #d9d9d9 !important;
  border-radius: 6px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  z-index: 9999 !important;
}

.brand-select .ant-select-item-option {
  background-color: #ffffff !important;
  color: #333333 !important;
  padding: 8px 12px !important;
}

.brand-select .ant-select-item-option:hover {
  background-color: #f5f5f5 !important;
}

.brand-select .ant-select-item-option-selected {
  background-color: #e6f7ff !important;
  color: #1890ff !important;
}

/* 多U设备全新布局样式 */
.device-multi-u-layout {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 0.3rem 0.4rem;
  gap: 0.2rem;
  color: white;
  overflow: visible;
  box-sizing: border-box;
  min-height: fit-content;
}

/* 头部区域 */
.device-header-new {
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 0.15rem;
  flex-shrink: 0;
  min-height: 0;
}

.device-title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.2rem;
  min-height: 0;
}

.device-name-large {
  font-size: 0.9rem;
  font-weight: 700;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  min-width: 0;
  max-width: calc(100% - 120px);
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 大型设备指示器样式已移除 */

.device-size-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.1rem 0.4rem;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* 主要信息区域 */
.device-main-info {
  display: flex;
  flex-direction: column;
  gap: 0.15rem;
  flex: 1;
  min-height: 0;
  overflow: visible;
}

/* 设备连接部分样式已移除 */

.device-system-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.5rem;
}

.system-info,
.device-type-info {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.system-badge {
  font-size: 0.65rem;
  font-weight: 700;
  padding: 0.1rem 0.3rem;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.type-value {
  font-size: 0.7rem;
  font-weight: 600;
  opacity: 0.9;
}

/* 硬件信息区域 */
.device-hardware-info {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  padding: 0.15rem;
  border: 1px solid rgba(255, 255, 255, 0.15);
  flex-shrink: 0;
  overflow: hidden;
}

.hardware-brand-model {
  display: flex;
  gap: 0.2rem;
  margin-bottom: 0.1rem;
  overflow: hidden;
}

.brand-text {
  font-size: 0.6rem;
  font-weight: 700;
  background: rgba(59, 130, 246, 0.3);
  padding: 0.05rem 0.2rem;
  border-radius: 3px;
  border: 1px solid rgba(59, 130, 246, 0.4);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  min-width: 0;
  line-height: 1.2;
}

.model-text {
  font-size: 0.6rem;
  font-weight: 600;
  background: rgba(16, 185, 129, 0.3);
  padding: 0.05rem 0.2rem;
  border-radius: 3px;
  border: 1px solid rgba(16, 185, 129, 0.4);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  min-width: 0;
  line-height: 1.2;
}

.hardware-specs {
  font-size: 0.6rem;
  line-height: 1.3;
  opacity: 0.95;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

/* 管理信息区域 */
.device-management-info {
  border-top: 1px solid rgba(255, 255, 255, 0.15);
  padding-top: 0.1rem;
  flex-shrink: 0;
  overflow: hidden;
}

.management-row {
  display: flex;
  gap: 0.3rem;
  margin-bottom: 0.1rem;
  overflow: hidden;
}

.management-item,
.asset-info {
  display: flex;
  align-items: center;
  gap: 0.15rem;
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.mgmt-icon,
.asset-icon {
  font-size: 0.6rem;
  opacity: 0.8;
  flex-shrink: 0;
}

.mgmt-label,
.asset-label {
  font-size: 0.6rem;
  opacity: 0.85;
  font-weight: 600;
  flex-shrink: 0;
  line-height: 1.3;
  color: rgba(255, 255, 255, 0.85);
}

.mgmt-value,
.asset-value {
  font-size: 0.55rem;
  font-weight: 600;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  min-width: 0;
  line-height: 1.2;
}

.device-name-compact {
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1.1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
  min-width: 0;
}

.device-ip-compact {
  font-size: 0.55rem;
  background: rgba(255, 255, 255, 0.15);
  padding: 0.1rem 0.3rem;
  border-radius: 4px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;
  flex-shrink: 0;
}

.device-os-compact {
  font-size: 0.55rem;
  background: rgba(255, 255, 255, 0.25);
  padding: 0.1rem 0.3rem;
  border-radius: 4px;
  font-weight: 600;
  white-space: nowrap;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  flex-shrink: 0;
}

.device-meta-item {
  font-size: 0.5rem;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.1);
  padding: 0.05rem 0.2rem;
  border-radius: 3px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 60px;
}

.device-asset-number {
  font-size: 0.45rem;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(255, 255, 255, 0.08);
  padding: 0.05rem 0.2rem;
  border-radius: 3px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80px;
}

/* 新增的分两列布局样式 */
.device-details-row {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  margin-top: 0.1rem;
}

.device-status-row {
  display: flex;
  align-items: center;
  gap: 0.2rem;
  margin-bottom: 0.1rem;
}

.device-info-row {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.1rem;
  font-size: 0.45rem;
  opacity: 0.8;
}

.device-owner-compact,
.device-dept-compact {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 60px;
  font-size: 0.45rem;
  opacity: 0.9;
}

/* 保留原有的紧凑布局样式 */
.device-details-compact {
  display: flex;
  align-items: center;
  gap: 0.2rem;
  font-size: 0.55rem;
  opacity: 0.9;
  line-height: 1;
  margin-top: 0.1rem;
}

.device-separator {
  opacity: 0.6;
  margin: 0 0.1rem;
}

/* PDU指示器样式已移除 */

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.1rem;
  width: 100%;
}

.device-icon {
  width: 16px;
  height: 16px;
  opacity: 0.9;
}

.device-status {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #48bb78;
  box-shadow: 0 0 4px rgba(72, 187, 120, 0.6);
}

.status-dot.online {
  background: #48bb78;
}

.device-info {
  flex: 1;
  text-align: center;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.1rem;
}

.device-name {
  font-size: 0.75rem;
  font-weight: 600;
  text-align: center;
  word-wrap: break-word;
  line-height: 1.2;
  max-width: 100%;
}

.device-ip {
  font-size: 0.625rem;
  opacity: 0.9;
  text-align: center;
  word-wrap: break-word;
}

/* 简化设备样式 */
.device-simple {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.2rem;
  height: 100%;
  padding: 0.3rem;
  text-align: center;
}

.device-simple .device-name {
  font-size: 0.75rem;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  word-wrap: break-word;
  line-height: 1.2;
  max-width: 100%;
}

.device-simple .device-ip {
  font-size: 0.625rem;
  opacity: 0.9;
  color: #e5e7eb;
  word-wrap: break-word;
}

.device-owner {
  font-size: 0.6rem;
  opacity: 0.8;
  text-align: center;
  color: #e2f4ff;
  word-wrap: break-word;
}

.device-specs {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.625rem;
  margin-top: 0.1rem;
}

.device-os {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
  font-weight: 500;
}

.device-units {
  opacity: 0.8;
}

/* 新增的设备信息样式 */
.device-details-row {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.625rem;
  margin-top: 0.1rem;
}

.device-status-text {
  padding: 0.125rem 0.25rem;
  border-radius: 3px;
  font-weight: 500;
  font-size: 0.55rem;
}

.device-status-text.online {
  background: rgba(16, 185, 129, 0.3);
  color: #10b981;
}

.device-status-text.offline {
  background: rgba(239, 68, 68, 0.3);
  color: #ef4444;
}

.device-status-text.maintenance {
  background: rgba(245, 158, 11, 0.3);
  color: #f59e0b;
}

.device-status-text.fault {
  background: rgba(239, 68, 68, 0.3);
  color: #ef4444;
}

.device-department {
  font-size: 0.55rem;
  opacity: 0.8;
  text-align: center;
  color: #e2f4ff;
  margin-top: 0.1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.2rem;
}

.device-pdu-info {
  font-size: 0.55rem;
  opacity: 0.9;
  text-align: center;
  color: #10b981;
  margin-top: 0.1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.2rem;
}

.owner-icon,
.dept-icon,
.pdu-icon {
  font-size: 0.6rem;
}

/* 状态点样式增强 */
.status-dot.offline {
  background: #ef4444;
  box-shadow: 0 0 6px rgba(239, 68, 68, 0.6);
}

.status-dot.maintenance {
  background: #f59e0b;
  box-shadow: 0 0 6px rgba(245, 158, 11, 0.6);
}

.status-dot.fault {
  background: #ef4444;
  box-shadow: 0 0 6px rgba(239, 68, 68, 0.6);
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 侧边面板 */
.side-panel {
  width: 320px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* 设备详情面板 */
.device-detail-panel {
  width: 320px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.panel-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #2d3748;
}

.panel-icon {
  color: #667eea;
}

/* 详情面板样式 */
.detail-panel-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.detail-panel-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #2d3748;
}

.detail-panel-icon {
  color: #667eea;
}

.expanded-device-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.detail-panel-placeholder {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: #718096;
}

.placeholder-icon {
  color: #cbd5e0;
}

.placeholder-content h4 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #4a5568;
}

.placeholder-content p {
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* 设备列表 */
.device-list {
  max-height: 300px;
  overflow-y: auto;
}

.device-list-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 0.5rem;
  border: 1px solid transparent;
}

.device-list-item:hover {
  background: rgba(103, 126, 234, 0.1);
  border-color: rgba(103, 126, 234, 0.3);
}

.device-list-item.active {
  background: rgba(103, 126, 234, 0.15);
  border-color: #667eea;
}

.device-list-icon {
  color: #667eea;
}

.device-list-info {
  flex: 1;
}

.device-list-name {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.25rem;
}

.device-list-meta {
  font-size: 0.8rem;
  color: #718096;
}

.device-list-status {
  display: flex;
  align-items: center;
}

/* 设备详情 */
.device-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item label {
  font-weight: 500;
  color: #4a5568;
  font-size: 0.9rem;
}

.detail-item span {
  font-weight: 600;
  color: #2d3748;
  font-size: 0.9rem;
}

/* 按钮样式 */
.action-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  /* 禁用拖拽以保持按钮功能 */
  -webkit-app-region: no-drag;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.action-btn.primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(103, 126, 234, 0.4);
}

.action-btn.secondary {
  background: #e2e8f0;
  color: #4a5568;
  border: 1px solid #cbd5e0;
}

.action-btn.secondary:hover:not(:disabled) {
  background: #cbd5e0;
  transform: translateY(-1px);
}

.action-btn.danger {
  background: linear-gradient(135deg, #f56565, #e53e3e);
  color: white;
}

.action-btn.danger:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 101, 101, 0.4);
}

.action-btn.warning {
  background: linear-gradient(135deg, #ff8c00, #ff6b35);
  color: white;
  border: 1px solid #ff7f00;
}

.action-btn.warning:hover:not(:disabled) {
  background: linear-gradient(135deg, #ff7f00, #ff5722);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 140, 0, 0.4);
}

.btn-icon {
  width: 16px;
  height: 16px;
}

/* 表单样式 */
.device-form {
  max-height: 70vh;
  overflow-y: auto;
  padding-right: 8px;
}

/* PDU连接配置样式已移除 */

.form-section {
  margin-bottom: 24px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  overflow: hidden;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-bottom: 1px solid #e8e8e8;
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.section-title i {
  width: 16px;
  height: 16px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.section-title .icon-info {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%234a90e2"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/></svg>');
}

.section-title .icon-hardware {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2350c878"><path d="M20 3H4c-1.1 0-2 .9-2 2v11c0 1.1.9 2 2 2h3l-1 1v2h12v-2l-1-1h3c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 13H4V5h16v11z"/></svg>');
}

.section-title .icon-network {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23f39c12"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/></svg>');
}

.section-title .icon-auth {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23e74c3c"><path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z"/></svg>');
}

.section-title .icon-rack {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%239b59b6"><path d="M4 6H2v14c0 1.1.9 2 2 2h14v-2H4V6zm16-4H8c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-1 9H9V9h10v2zm0-4H9V5h10v2z"/></svg>');
}

.section-title .icon-manage {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2334495e"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>');
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  padding: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-label {
  font-weight: 500;
  color: #2c3e50;
  font-size: 13px;
  margin-bottom: 4px;
}

.form-label .required {
  color: #e74c3c;
  margin-left: 2px;
}

.form-input {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
  transition: all 0.2s ease;
}

.form-input:hover {
  border-color: #4a90e2;
}

.form-input:focus {
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
}

.form-hint {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #fff7e6 0%, #ffeaa7 100%);
  border: 1px solid #ffd591;
  border-radius: 8px;
  color: #d46b08;
  font-size: 13px;
  font-weight: 500;
}

.form-hint .icon-warning {
  width: 16px;
  height: 16px;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23d46b08"><path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }

  .side-panel {
    width: 100%;
  }

  .rack-main {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
  }
}

@media (max-width: 768px) {
  .rack-detail-page {
    padding: 0;
  }

  .page-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .main-content {
    padding: 0 1rem 1rem;
  }

  .rack-info-bar {
    margin: 1rem;
    flex-direction: column;
    text-align: center;
  }

  .rack-container {
    flex-direction: column;
    align-items: center;
  }

  .pdu-column {
    width: 100%;
    max-width: 400px;
    height: 60px;
  }

  .pdu-body {
    display: flex;
    max-height: none;
    overflow-x: auto;
  }

  .pdu-unit {
    width: 20px;
    height: 100%;
    border-right: 1px solid #e2e8f0;
    border-bottom: none;
  }

  .form-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .device-form {
    max-height: 60vh;
  }
}
</style>
