<template>
  <div class="rack-layout-page">
    <!-- 顶部导航栏 -->
    <header class="page-header">
      <div class="header-left">
        <button
          class="back-btn"
          @click="goBack"
        >
          <svg
            class="back-icon"
            viewBox="0 0 24 24"
            fill="none"
          >
            <path
              d="M19 12H5M12 19l-7-7 7-7"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
          <span>返回机房管理</span>
        </button>
        <div class="breadcrumb">
          <span class="breadcrumb-item">机房管理</span>
          <span class="breadcrumb-separator">/</span>
          <span class="breadcrumb-current">机柜布局</span>
        </div>
      </div>

      <div class="header-center">
        <h1 class="page-title">
          <svg
            class="title-icon"
            viewBox="0 0 24 24"
            fill="none"
          >
            <rect
              x="3"
              y="4"
              width="18"
              height="16"
              rx="2"
              stroke="currentColor"
              stroke-width="2"
            />
            <path
              d="M7 8h10M7 12h10M7 16h6"
              stroke="currentColor"
              stroke-width="2"
            />
          </svg>
          机柜布局管理
        </h1>
        <p class="page-subtitle">拖拽调整机柜位置，双击进入机柜详情</p>
      </div>

      <div class="header-actions">
        <button
          class="action-btn secondary"
          @click="resetLayout"
        >
          <svg
            class="btn-icon"
            viewBox="0 0 24 24"
            fill="none"
          >
            <path
              d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"
              stroke="currentColor"
              stroke-width="2"
            />
            <path
              d="M21 3v5h-5M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"
              stroke="currentColor"
              stroke-width="2"
            />
            <path
              d="M8 16h-5v5"
              stroke="currentColor"
              stroke-width="2"
            />
          </svg>
          重置布局
        </button>
        <button
          class="action-btn primary"
          @click="saveLayout"
        >
          <svg
            class="btn-icon"
            viewBox="0 0 24 24"
            fill="none"
          >
            <path
              d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"
              stroke="currentColor"
              stroke-width="2"
            />
            <polyline
              points="17,21 17,13 7,13 7,21"
              stroke="currentColor"
              stroke-width="2"
            />
            <polyline
              points="7,3 7,8 15,8"
              stroke="currentColor"
              stroke-width="2"
            />
          </svg>
          保存布局
        </button>
      </div>
    </header>

    <!-- 机柜统计信息 -->
    <div class="stats-bar">
      <div class="stat-item">
        <div class="stat-value">{{ racks.length }}</div>
        <div class="stat-label">总机柜数</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ occupiedRacks }}</div>
        <div class="stat-label">已使用</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ racks.length - occupiedRacks }}</div>
        <div class="stat-label">空闲</div>
      </div>
      <div class="stat-item">
        <div class="stat-value">{{ Math.round((occupiedRacks / racks.length) * 100) || 0 }}%</div>
        <div class="stat-label">使用率</div>
      </div>

      <!-- 跨机柜拖拽状态提示 -->
      <div
        v-if="crossRackDragActive"
        class="drag-status"
      >
        <div class="drag-indicator">
          <svg
            class="drag-icon"
            viewBox="0 0 24 24"
            fill="none"
          >
            <path
              d="M7 7h10v10H7z"
              stroke="currentColor"
              stroke-width="2"
              fill="rgba(74, 144, 226, 0.2)"
            />
            <path
              d="M14 14l6-6M20 14v-6h-6"
              stroke="currentColor"
              stroke-width="2"
            />
          </svg>
          <span>设备拖拽中：{{ crossRackDragDevice?.name }}</span>
        </div>
      </div>
    </div>

    <!-- 机柜网格布局 -->
    <section class="rack-container">
      <div
        class="rack-grid"
        @dragover.prevent
      >
        <div
          v-for="(rack, index) in racks"
          :key="rack.id"
          class="rack-card"
          :class="{
            dragging: draggingIndex === index,
            'drop-target': dragOverIndex === index && draggingIndex !== index,
            occupied: isRackOccupied(rack)
          }"
          draggable="true"
          :title="`双击进入机柜 ${rack.name}`"
          @dragstart="onDragStart(index)"
          @dragover.prevent="onDragOver(index)"
          @dragleave="onDragLeave"
          @drop.prevent="onDrop(index)"
          @dblclick="openRackDetail(rack)"
        >
          <div class="rack-header">
            <div
              class="rack-status"
              :class="isRackOccupied(rack) ? 'occupied' : 'empty'"
            ></div>
            <div class="rack-name-container">
              <input
                v-if="editingRackId === rack.id"
                ref="rackNameInput"
                v-model="editingRackName"
                class="rack-name-input"
                @blur="saveRackName(rack)"
                @keyup.enter="saveRackName(rack)"
                @keyup.escape="cancelEdit()"
                @click.stop
              />
              <div
                v-else
                class="rack-name"
                :title="'点击编辑机柜名称'"
                @click.stop="startEditRackName(rack)"
              >
                {{ rack.name }}
              </div>
              <button
                v-if="editingRackId !== rack.id"
                class="edit-name-btn"
                title="编辑机柜名称"
                @click.stop="startEditRackName(rack)"
              >
                <svg
                  viewBox="0 0 24 24"
                  fill="none"
                >
                  <path
                    d="M11 4H4a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2v-7"
                    stroke="currentColor"
                    stroke-width="2"
                  />
                  <path
                    d="M18.5 2.5a2.121 2.121 0 013 3L12 15l-4 1 1-4 9.5-9.5z"
                    stroke="currentColor"
                    stroke-width="2"
                  />
                </svg>
              </button>
            </div>
            <div class="rack-actions">
              <button
                class="rack-action-btn"
                title="查看详情"
                @click.stop="openRackDetail(rack)"
              >
                <svg
                  viewBox="0 0 24 24"
                  fill="none"
                >
                  <path
                    d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"
                    stroke="currentColor"
                    stroke-width="2"
                  />
                  <circle
                    cx="12"
                    cy="12"
                    r="3"
                    stroke="currentColor"
                    stroke-width="2"
                  />
                </svg>
              </button>
              <button
                class="rack-action-btn delete-btn"
                title="删除机柜"
                @click.stop="deleteRack(rack)"
              >
                <svg
                  viewBox="0 0 24 24"
                  fill="none"
                >
                  <path
                    d="M3 6h18"
                    stroke="currentColor"
                    stroke-width="2"
                  />
                  <path
                    d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"
                    stroke="currentColor"
                    stroke-width="2"
                  />
                  <path
                    d="M8 6V4c0-1 1-2 2-2h4c0-1 1-2 2-2v2"
                    stroke="currentColor"
                    stroke-width="2"
                  />
                </svg>
              </button>
            </div>
          </div>

          <div class="rack-body">
            <div class="rack-visual">
              <div class="rack-units">
                <div
                  v-for="u in 8"
                  :key="u"
                  class="unit-indicator"
                  :class="{ filled: Math.random() > 0.7 }"
                ></div>
              </div>
            </div>

            <div class="rack-info">
              <div class="rack-meta">
                <span class="rack-id">{{ rack.id }}</span>
                <span class="rack-capacity">42U</span>
              </div>
              <div class="rack-stats">
                <div class="usage-bar">
                  <div
                    class="usage-fill"
                    :style="{ width: getUsagePercentage(rack) + '%' }"
                  ></div>
                </div>
                <span class="usage-text">{{ getUsagePercentage(rack) }}% 使用</span>
              </div>
            </div>
          </div>

          <div class="rack-footer">
            <span class="drag-hint">拖拽调整位置</span>
          </div>
        </div>

        <!-- 空状态 -->
        <div
          v-if="!racks.length"
          class="empty-state"
        >
          <div class="empty-icon">
            <svg
              viewBox="0 0 24 24"
              fill="none"
            >
              <rect
                x="3"
                y="4"
                width="18"
                height="16"
                rx="2"
                stroke="currentColor"
                stroke-width="2"
              />
              <path
                d="M7 8h10M7 12h10M7 16h6"
                stroke="currentColor"
                stroke-width="2"
              />
            </svg>
          </div>
          <h3 class="empty-title">暂无机柜</h3>
          <p class="empty-description">请在机房配置中设置机柜数量，或联系管理员添加机柜</p>
          <button
            class="empty-action"
            @click="goToRoomConfig"
          >
            <svg
              class="btn-icon"
              viewBox="0 0 24 24"
              fill="none"
            >
              <circle
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="2"
              />
              <line
                x1="12"
                y1="8"
                x2="12"
                y2="16"
                stroke="currentColor"
                stroke-width="2"
              />
              <line
                x1="8"
                y1="12"
                x2="16"
                y2="12"
                stroke="currentColor"
                stroke-width="2"
              />
            </svg>
            配置机柜
          </button>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
/**
 * 文件: RoomRackLayout.vue
 * 功能: 展示并拖拽调整机房内机柜的布局顺序；双击进入机柜详情。
 * 依赖: Vue 3 Composition API, vue-router
 * 作者: AI 助手
 * 修改时间: 自动生成
 */
import { ref, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import eventBus from '../../utils/eventBus'

interface RackItem {
  id: string
  name: string
}

const route = useRoute()
const router = useRouter()

const roomId = String(route.params.roomId || '')
const layoutStorageKey = `rack_layout_${roomId}`

const racks = ref<RackItem[]>([])
const draggingIndex = ref<number | null>(null)
const dragOverIndex = ref<number | null>(null)
const occupiedRacks = ref(0)

// 跨机柜拖拽状态
const crossRackDragActive = ref(false)
const crossRackDragDevice = ref<any>(null)

// 机柜名称编辑状态
const editingRackId = ref<string | null>(null)
const editingRackName = ref('')

/**
 * 从本地存储加载布局；若不存在，根据路由 query.rackCount 生成默认机柜
 */
function loadLayout() {
  try {
    const cache = localStorage.getItem(layoutStorageKey)
    if (cache) {
      racks.value = JSON.parse(cache)
      return
    }
  } catch (e) {
    console.error('读取机柜布局失败:', e)
  }

  const count = Number(route.query.rackCount || 0)
  if (count > 0) {
    racks.value = Array.from({ length: count }).map((_, i) => ({
      id: `${roomId}-R${String(i + 1).padStart(2, '0')}`,
      name: `机柜 ${String(i + 1).padStart(2, '0')}`
    }))
  } else {
    racks.value = []
  }
}

/**
 * 保存布局到本地存储
 */
function saveLayout() {
  try {
    localStorage.setItem(layoutStorageKey, JSON.stringify(racks.value))
  } catch (e) {
    console.error('保存机柜布局失败:', e)
  }
}

function onDragStart(index: number) {
  draggingIndex.value = index
}

function onDragOver(index: number) {
  dragOverIndex.value = index
}

function onDragLeave() {
  dragOverIndex.value = null
}

function onDrop(index: number) {
  if (draggingIndex.value === null || draggingIndex.value === index) return
  const list = [...racks.value]
  const [moved] = list.splice(draggingIndex.value, 1)
  list.splice(index, 0, moved)
  racks.value = list
  draggingIndex.value = null
  dragOverIndex.value = null
}

/**
 * 双击打开机柜详情
 */
function openRackDetail(rack: RackItem) {
  try {
    router.push({ name: 'RackDetail', params: { roomId, rackId: rack.id } })
  } catch (e) {
    console.error('进入机柜详情失败:', e)
  }
}

/**
 * 删除机柜
 */
function deleteRack(rack: RackItem) {
  if (confirm(`确定要删除机柜 ${rack.name} 吗？`)) {
    racks.value = racks.value.filter((r) => r.id !== rack.id)
    saveLayout()
  }
}

/**
 * 开始编辑机柜名称
 */
function startEditRackName(rack: RackItem) {
  editingRackId.value = rack.id
  editingRackName.value = rack.name
  // 下一帧聚焦输入框
  nextTick(() => {
    const input = document.querySelector('.rack-name-input') as HTMLInputElement
    if (input) {
      input.focus()
      input.select()
    }
  })
}

/**
 * 保存机柜名称
 */
function saveRackName(rack: RackItem) {
  if (editingRackName.value.trim()) {
    rack.name = editingRackName.value.trim()
    saveLayout()
  }
  cancelEdit()
}

/**
 * 取消编辑
 */
function cancelEdit() {
  editingRackId.value = null
  editingRackName.value = ''
}

function goBack() {
  router.back()
}

/**
 * 重置机柜布局到默认顺序
 */
function resetLayout() {
  const count = Number(route.query.rackCount || 0)
  if (count > 0) {
    racks.value = Array.from({ length: count }).map((_, i) => ({
      id: `${roomId}-R${String(i + 1).padStart(2, '0')}`,
      name: `机柜 ${String(i + 1).padStart(2, '0')}`
    }))
  }
}

/**
 * 跳转到机房配置页面
 */
function goToRoomConfig() {
  router.push({ name: 'RoomManagement' })
}

/**
 * 检查机柜是否被占用（基于实际设备数据）
 */
function isRackOccupied(rack: RackItem): boolean {
  try {
    // 使用与机柜详情页面一致的存储键
    const storageKey = `rack_detail_${rack.id}`
    const devicesData = localStorage.getItem(storageKey)

    if (!devicesData) {
      return false // 没有设备数据时视为未占用
    }

    const devices = JSON.parse(devicesData)
    if (!Array.isArray(devices)) {
      return false
    }

    // 检查是否有有效设备
    return devices.some((device) => device && typeof device.uHeight === 'number' && device.uHeight > 0)
  } catch (error) {
    console.warn(`检查机柜 ${rack.id} 占用状态失败:`, error)
    return false
  }
}

/**
 * 获取机柜使用率（从localStorage读取实际设备数据）
 */
function getUsagePercentage(rack: RackItem): number {
  try {
    // 使用与机柜详情页面一致的存储键
    const storageKey = `rack_detail_${rack.id}`
    const devicesData = localStorage.getItem(storageKey)

    if (!devicesData) {
      console.log(`机柜 ${rack.id} 没有设备数据`)
      return 0 // 没有设备数据时使用率为0
    }

    const devices = JSON.parse(devicesData)
    if (!Array.isArray(devices)) {
      console.log(`机柜 ${rack.id} 设备数据格式错误`)
      return 0
    }

    // 计算已使用的U位数
    const usedUnits = devices
      .filter((device) => device && typeof device.uHeight === 'number' && device.uHeight > 0)
      .reduce((total, device) => total + device.uHeight, 0)

    // 计算使用率（总U位数为42）
    const percentage = Math.round((usedUnits / 42) * 100)
    console.log(`机柜 ${rack.id} 使用率计算: ${usedUnits}U / 42U = ${percentage}%`)
    return Math.min(percentage, 100) // 确保不超过100%
  } catch (error) {
    console.warn(`获取机柜 ${rack.id} 使用率失败:`, error)
    return 0
  }
}

/**
 * 更新占用机柜数量
 */
function updateOccupiedRacks() {
  occupiedRacks.value = racks.value.filter((rack) => isRackOccupied(rack)).length
}

loadLayout()
updateOccupiedRacks()

// 自动保存（轻度防抖）
let saveTimer: number | null = null
watch(
  racks,
  () => {
    updateOccupiedRacks()
    if (saveTimer) window.clearTimeout(saveTimer)
    saveTimer = window.setTimeout(async () => {
      saveLayout()
      // 更新机房使用率
      try {
        const { unifiedDataService } = await import('../../services/unifiedEnterpriseDataService')
        unifiedDataService.updateAllRoomUtilizations()
        console.log('机房使用率已更新')
      } catch (error) {
        console.warn('更新机房使用率失败:', error)
      }
    }, 300)
  },
  { deep: true }
)

// 组件挂载时监听跨机柜拖拽事件
onMounted(() => {
  // 监听设备拖拽开始事件
  eventBus.on('device-drag-start', (data: any) => {
    crossRackDragActive.value = true
    crossRackDragDevice.value = data.device
  })

  // 监听设备拖拽结束事件
  eventBus.on('device-drag-end', () => {
    crossRackDragActive.value = false
    crossRackDragDevice.value = null
  })
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  eventBus.off('device-drag-start')
  eventBus.off('device-drag-end')
})
</script>

<style scoped>
/* 页面整体布局 */
.rack-layout-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow-y: auto;
}

/* 顶部导航栏 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #fff;
  color: #475569;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.back-btn:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.back-icon {
  width: 16px;
  height: 16px;
  color: #64748b;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #64748b;
}

.breadcrumb-separator {
  color: #cbd5e1;
}

.breadcrumb-current {
  color: #3b82f6;
  font-weight: 500;
}

.header-center {
  text-align: center;
  flex: 1;
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
}

.title-icon {
  width: 28px;
  height: 28px;
  color: #3b82f6;
}

.page-subtitle {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #64748b;
  font-weight: 400;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.action-btn.primary {
  background: #3b82f6;
  color: white;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.action-btn.primary:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.4);
}

.action-btn.secondary {
  background: #f8fafc;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.action-btn.secondary:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.btn-icon {
  width: 16px;
  height: 16px;
}

/* 统计信息栏 */
.stats-bar {
  display: flex;
  gap: 24px;
  padding: 20px 24px;
  background: rgba(255, 255, 255, 0.9);
  margin: 0 24px 24px 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 跨机柜拖拽状态提示 */
.drag-status {
  margin-left: auto;
  padding: 8px 16px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.drag-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.drag-icon {
  width: 20px;
  height: 20px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.stat-item {
  text-align: center;
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 机柜容器 */
.rack-container {
  flex: 1;
  padding: 0 24px 24px 24px;
  overflow-y: auto;
  max-height: calc(100vh - 120px);
}

.rack-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  min-height: auto;
}

/* 机柜卡片 */
.rack-card {
  background: #fff;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: grab;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.rack-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: #3b82f6;
}

.rack-card.dragging {
  opacity: 0.6;
  transform: rotate(5deg) scale(0.95);
  cursor: grabbing;
}

.rack-card.drop-target {
  border-color: #10b981;
  background: #f0fdf4;
  transform: scale(1.02);
}

.rack-card.occupied {
  border-left: 4px solid #f59e0b;
}

.rack-card.occupied .rack-status.occupied {
  background: #f59e0b;
}

/* 机柜头部 */
.rack-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.rack-status {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.rack-status.empty {
  background: #6b7280;
  box-shadow: 0 0 0 2px rgba(107, 114, 128, 0.2);
}

.rack-name-container {
  display: flex;
  align-items: center;
  flex: 1;
  margin-left: 12px;
  gap: 8px;
}

.rack-name {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  flex: 1;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.rack-name:hover {
  background-color: rgba(59, 130, 246, 0.1);
}

.rack-name-input {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  flex: 1;
  border: 2px solid #3b82f6;
  border-radius: 4px;
  padding: 4px 8px;
  background: white;
  outline: none;
}

.edit-name-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  color: #6b7280;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0;
}

.rack-name-container:hover .edit-name-btn {
  opacity: 1;
}

.edit-name-btn:hover {
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.edit-name-btn svg {
  width: 14px;
  height: 14px;
}

.rack-actions {
  display: flex;
  gap: 8px;
}

.rack-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: #e2e8f0;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.rack-action-btn:hover {
  background: #3b82f6;
  color: white;
}

.rack-action-btn svg {
  width: 16px;
  height: 16px;
}

.rack-action-btn.delete-btn {
  background: #fee2e2;
  color: #dc2626;
}

.rack-action-btn.delete-btn:hover {
  background: #dc2626;
  color: white;
}

/* 机柜主体 */
.rack-body {
  padding: 20px;
}

.rack-visual {
  margin-bottom: 16px;
}

.rack-units {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 2px;
  padding: 8px;
  background: #f1f5f9;
  border-radius: 6px;
}

.unit-indicator {
  height: 8px;
  background: #e2e8f0;
  border-radius: 2px;
  transition: all 0.2s ease;
}

.unit-indicator.filled {
  background: #3b82f6;
}

.rack-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.rack-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rack-id {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

.rack-capacity {
  font-size: 12px;
  color: #3b82f6;
  font-weight: 600;
  background: #dbeafe;
  padding: 2px 8px;
  border-radius: 12px;
}

.rack-stats {
  display: flex;
  align-items: center;
  gap: 8px;
}

.usage-bar {
  flex: 1;
  height: 6px;
  background: #e2e8f0;
  border-radius: 3px;
  overflow: hidden;
}

.usage-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #3b82f6);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.usage-text {
  font-size: 11px;
  color: #64748b;
  font-weight: 500;
  min-width: 50px;
  text-align: right;
}

/* 机柜底部 */
.rack-footer {
  padding: 12px 20px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  text-align: center;
}

.drag-hint {
  font-size: 11px;
  color: #94a3b8;
  font-weight: 500;
}

/* 空状态 */
.empty-state {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  width: 80px;
  height: 80px;
  margin-bottom: 24px;
  color: #cbd5e1;
}

.empty-icon svg {
  width: 100%;
  height: 100%;
}

.empty-title {
  font-size: 20px;
  font-weight: 600;
  color: #475569;
  margin: 0 0 8px 0;
}

.empty-description {
  font-size: 14px;
  color: #64748b;
  margin: 0 0 24px 0;
  max-width: 400px;
  line-height: 1.5;
}

.empty-action {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.empty-action:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    padding: 16px;
  }

  .header-left,
  .header-actions {
    width: 100%;
    justify-content: center;
  }

  .stats-bar {
    margin: 0 16px 16px 16px;
    padding: 16px;
  }

  .rack-container {
    padding: 0 16px 16px 16px;
  }

  .rack-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
</style>
