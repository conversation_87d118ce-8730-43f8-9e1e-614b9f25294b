/**
 * 数据迁移服务
 * 负责将现有各模块的数据迁移到统一的企业数据管理服务中
 */

import unifiedDataService, { HostResource, NetworkDevice, AssetResource, RoomResource, MonitorTarget } from './unifiedEnterpriseDataService'
import eventBus from '../utils/eventBus'

export interface MigrationResult {
  success: boolean
  migratedCount: number
  errors: string[]
  module: string
  timestamp: string
}

export interface MigrationProgress {
  module: string
  current: number
  total: number
  percentage: number
  status: 'pending' | 'running' | 'completed' | 'failed'
}

class DataMigrationService {
  private migrationHistory: MigrationResult[] = []
  private currentMigrations: Map<string, MigrationProgress> = new Map()

  /**
   * 执行完整的数据迁移
   */
  async migrateAllData(): Promise<void> {
    console.log('开始数据迁移...')

    try {
      // 迁移机房数据
      await this.migrateRoomData()

      // 迁移固定资产数据
      await this.migrateAssetData()

      // 迁移网络设备数据
      await this.migrateNetworkDeviceData()

      // 迁移监控目标数据
      await this.migrateMonitorTargetData()

      // 迁移主机数据（从现有的主机管理中）
      await this.migrateHostData()

      console.log('数据迁移完成！')

      // 标记迁移完成
      localStorage.setItem('unified_data_migration_completed', 'true')
    } catch (error) {
      console.error('数据迁移失败:', error)
      throw error
    }
  }

  /**
   * 检查是否需要迁移
   */
  needsMigration(): boolean {
    return !localStorage.getItem('unified_data_migration_completed')
  }

  /**
   * 迁移机房数据
   */
  private async migrateRoomData(): Promise<void> {
    try {
      const roomData = localStorage.getItem('room_management_data')

      if (roomData) {
        // 迁移现有机房数据
        const rooms = JSON.parse(roomData)
        console.log('迁移机房数据:', rooms.length, '个机房')

        for (const room of rooms) {
          const roomResource: Omit<RoomResource, 'id' | 'createdAt' | 'updatedAt'> = {
            name: room.name,
            type: 'room',
            status: room.status || 'normal',
            location: room.location,
            area: room.area,
            rackCount: room.rackCount,
            temperature: room.temperature,
            humidity: room.humidity,
            utilization: room.utilization,
            description: room.description,
            tags: room.tags || []
          }

          unifiedDataService.addRoom(roomResource)
        }
        console.log('机房数据迁移完成')
      } else {
        // 创建默认机房数据
        console.log('创建默认机房数据...')
        const defaultRooms = [
          {
            name: '广州',
            type: 'room' as const,
            status: 'normal' as const,
            location: 'C4',
            area: 200,
            rackCount: 10,
            temperature: 22,
            humidity: 45,
            utilization: 0,
            description: '主数据中心机房',
            tags: ['主机房', '生产环境']
          },
          {
            name: '深圳',
            type: 'room' as const,
            status: 'normal' as const,
            location: 'B2',
            area: 150,
            rackCount: 8,
            temperature: 23,
            humidity: 50,
            utilization: 0,
            description: '备份数据中心机房',
            tags: ['备份机房', '灾备环境']
          }
        ]

        for (const room of defaultRooms) {
          unifiedDataService.addRoom(room)
        }
        console.log('默认机房数据创建完成')
      }
    } catch (error) {
      console.error('机房数据迁移失败:', error)
    }
  }

  /**
   * 迁移固定资产数据
   */
  private async migrateAssetData(): Promise<void> {
    try {
      // 这里需要根据实际的固定资产数据存储位置来获取数据
      // 由于当前固定资产没有本地存储，我们创建一些示例数据
      const defaultAssets = [
        {
          assetNumber: 'IT-001',
          name: 'Dell OptiPlex 7090',
          category: 'computer',
          brand: 'Dell',
          model: 'OptiPlex 7090',
          serialNumber: '*********',
          status: 'normal',
          location: '北京办公室',
          owner: '张三',
          department: 'IT部门',
          purchaseDate: '2023-01-15',
          purchasePrice: 5999,
          warrantyExpiry: '2026-01-15',
          description: 'IT部门台式机'
        },
        {
          assetNumber: 'SV-001',
          name: 'Dell PowerEdge R740',
          category: 'server',
          brand: 'Dell',
          model: 'PowerEdge R740',
          serialNumber: 'PE740001',
          status: 'normal',
          location: '主机房A',
          owner: '运维团队',
          department: '技术部',
          purchaseDate: '2023-03-20',
          purchasePrice: 45000,
          warrantyExpiry: '2026-03-20',
          description: '生产环境服务器'
        }
      ]

      console.log('迁移固定资产数据:', defaultAssets.length, '个资产')

      for (const asset of defaultAssets) {
        const assetResource: Omit<AssetResource, 'id' | 'createdAt' | 'updatedAt'> = {
          name: asset.name,
          type: 'asset',
          status: asset.status as any,
          assetNumber: asset.assetNumber,
          category: asset.category as any,
          brand: asset.brand,
          model: asset.model,
          serialNumber: asset.serialNumber,
          location: asset.location,
          owner: asset.owner,
          department: asset.department,
          purchaseDate: asset.purchaseDate,
          purchasePrice: asset.purchasePrice,
          warrantyExpiry: asset.warrantyExpiry,
          description: asset.description,
          tags: []
        }

        unifiedDataService.addAsset(assetResource)
      }

      console.log('固定资产数据迁移完成')
    } catch (error) {
      console.error('固定资产数据迁移失败:', error)
    }
  }

  /**
   * 迁移网络设备数据
   */
  private async migrateNetworkDeviceData(): Promise<void> {
    try {
      // 创建一些示例网络设备数据
      const defaultNetworkDevices = [
        {
          name: '核心交换机-01',
          deviceType: 'switch',
          ip: '************',
          model: 'Cisco Catalyst 9300',
          serialNumber: 'CS9300001',
          macAddress: '00:1A:2B:3C:4D:01',
          firmwareVersion: '16.12.04',
          location: '主机房A',
          rackInfo: 'A-01-U42',
          status: 'online',
          snmpCommunity: 'public',
          sshPort: 22,
          managementInterface: '************'
        },
        {
          name: '边界路由器-01',
          deviceType: 'router',
          ip: '***********',
          model: 'Cisco ISR 4331',
          serialNumber: 'ISR4331001',
          macAddress: '00:1A:2B:3C:4D:02',
          firmwareVersion: '16.09.04',
          location: '主机房A',
          rackInfo: 'A-01-U40',
          status: 'online',
          snmpCommunity: 'public',
          sshPort: 22,
          managementInterface: '***********'
        },
        {
          name: '防火墙-01',
          deviceType: 'firewall',
          ip: '*************',
          model: '华为 USG6000V',
          serialNumber: 'USG6000001',
          firmwareVersion: 'V500R001C30',
          location: '主机房A',
          rackInfo: 'A-01-U38',
          status: 'online',
          snmpCommunity: 'public',
          sshPort: 22,
          managementInterface: '*************'
        }
      ]

      console.log('迁移网络设备数据:', defaultNetworkDevices.length, '个设备')

      for (const device of defaultNetworkDevices) {
        const networkDevice: Omit<NetworkDevice, 'id' | 'createdAt' | 'updatedAt'> = {
          name: device.name,
          type: 'network_device',
          status: device.status as any,
          ip: device.ip,
          deviceType: device.deviceType as any,
          model: device.model,
          serialNumber: device.serialNumber,
          macAddress: device.macAddress,
          firmwareVersion: device.firmwareVersion,
          location: device.location,
          rackInfo: device.rackInfo,
          snmpCommunity: device.snmpCommunity,
          sshPort: device.sshPort,
          managementInterface: device.managementInterface,
          description: `${device.deviceType} - ${device.model}`,
          tags: [device.deviceType, device.location]
        }

        unifiedDataService.addNetworkDevice(networkDevice)
      }

      console.log('网络设备数据迁移完成')
    } catch (error) {
      console.error('网络设备数据迁移失败:', error)
    }
  }

  /**
   * 迁移监控目标数据
   */
  private async migrateMonitorTargetData(): Promise<void> {
    try {
      // 创建一些示例监控目标数据
      const defaultMonitorTargets = [
        {
          name: '主网关延迟监控',
          targetType: 'latency',
          target: '***********',
          protocol: 'icmp',
          interval: 60,
          threshold: 50,
          enabled: true,
          status: 'online'
        },
        {
          name: '外网连接监控',
          targetType: 'latency',
          target: '*******',
          protocol: 'icmp',
          interval: 30,
          threshold: 100,
          enabled: true,
          status: 'online'
        },
        {
          name: '核心交换机性能监控',
          targetType: 'performance',
          target: '************',
          protocol: 'snmp',
          interval: 300,
          threshold: 80,
          enabled: true,
          status: 'online'
        }
      ]

      console.log('迁移监控目标数据:', defaultMonitorTargets.length, '个目标')

      for (const target of defaultMonitorTargets) {
        const monitorTarget: Omit<MonitorTarget, 'id' | 'createdAt' | 'updatedAt'> = {
          name: target.name,
          type: 'monitor_target',
          status: target.status as any,
          targetType: target.targetType as any,
          target: target.target,
          protocol: target.protocol,
          interval: target.interval,
          threshold: target.threshold,
          enabled: target.enabled,
          description: `${target.targetType}监控 - ${target.target}`,
          tags: [target.targetType, target.protocol]
        }

        unifiedDataService.addMonitorTarget(monitorTarget)
      }

      console.log('监控目标数据迁移完成')
    } catch (error) {
      console.error('监控目标数据迁移失败:', error)
    }
  }

  /**
   * 迁移主机数据
   */
  private async migrateHostData(): Promise<void> {
    try {
      // 创建一些示例主机数据
      const defaultHosts = [
        {
          name: 'Web服务器-01',
          hostname: 'web01.company.com',
          ip: '************0',
          port: 22,
          username: 'admin',
          os: 'Ubuntu',
          osVersion: '20.04 LTS',
          architecture: 'x86_64',
          location: '主机房A',
          owner: '运维团队',
          environment: 'production',
          status: 'online'
        },
        {
          name: '数据库服务器-01',
          hostname: 'db01.company.com',
          ip: '************1',
          port: 22,
          username: 'admin',
          os: 'CentOS',
          osVersion: '7.9',
          architecture: 'x86_64',
          location: '主机房A',
          owner: '数据库团队',
          environment: 'production',
          status: 'online'
        },
        {
          name: '测试服务器-01',
          hostname: 'test01.company.com',
          ip: '*************',
          port: 22,
          username: 'testuser',
          os: 'Ubuntu',
          osVersion: '22.04 LTS',
          architecture: 'x86_64',
          location: '主机房B',
          owner: '测试团队',
          environment: 'staging',
          status: 'online'
        }
      ]

      console.log('迁移主机数据:', defaultHosts.length, '个主机')

      for (const host of defaultHosts) {
        const hostResource: Omit<HostResource, 'id' | 'createdAt' | 'updatedAt'> = {
          name: host.name,
          type: 'host',
          status: host.status as any,
          ip: host.ip,
          hostname: host.hostname,
          port: host.port,
          username: host.username,
          os: host.os,
          osVersion: host.osVersion,
          architecture: host.architecture,
          location: host.location,
          owner: host.owner,
          environment: host.environment as any,
          description: `${host.os} ${host.osVersion} 服务器`,
          tags: [host.environment, host.os, host.location]
        }

        unifiedDataService.addHost(hostResource)
      }

      console.log('主机数据迁移完成')
    } catch (error) {
      console.error('主机数据迁移失败:', error)
    }
  }

  /**
   * 重置迁移状态（用于测试）
   */
  resetMigrationStatus(): void {
    localStorage.removeItem('unified_data_migration_completed')
    console.log('迁移状态已重置')
  }

  /**
   * 清理旧数据（可选，谨慎使用）
   */
  cleanupOldData(): void {
    const oldKeys = [
      'room_management_data'
      // 可以添加其他旧的存储键
    ]

    oldKeys.forEach((key) => {
      if (localStorage.getItem(key)) {
        console.log(`清理旧数据: ${key}`)
        localStorage.removeItem(key)
      }
    })
  }

  /**
   * 获取迁移统计信息
   */
  getMigrationStats() {
    return unifiedDataService.getStatistics()
  }

  /**
   * 迁移固定资产管理Store数据 - 专门处理assetManagementStore的数据
   */
  async migrateAssetManagementStoreData(): Promise<MigrationResult> {
    const module = 'AssetManagementStore'
    const result: MigrationResult = {
      success: false,
      migratedCount: 0,
      errors: [],
      module,
      timestamp: new Date().toISOString()
    }

    try {
      // 从localStorage读取现有资产数据
      const storedData = localStorage.getItem('chaterm_asset_management')
      if (!storedData) {
        result.errors.push('未找到现有资产管理数据')
        return result
      }

      const data = JSON.parse(storedData)
      const assets = data.assets || []

      if (assets.length === 0) {
        result.errors.push('资产数据为空')
        return result
      }

      // 更新迁移进度
      this.updateMigrationProgress(module, 0, assets.length, 'running')

      // 执行迁移
      let migratedCount = 0
      const errors: string[] = []

      for (let i = 0; i < assets.length; i++) {
        try {
          const asset = assets[i]

          // 检查是否已存在相同资产编号的数据
          const existingAssets = unifiedDataService.getAssets()
          const exists = existingAssets.some((a) => a.assetNumber === asset.assetNumber)

          if (!exists) {
            // 转换并添加资产
            const unifiedAsset = {
              name: asset.name,
              type: 'asset' as const,
              status: this.mapAssetStatus(asset.status),
              assetNumber: asset.assetNumber,
              category: asset.category,
              model: asset.model || '',
              assignedTo: asset.assignedTo,
              purchaseDate: asset.purchaseDate,
              purchasePrice: asset.purchasePrice,
              description: asset.description,
              // 硬件配置字段
              cpu: asset.cpu,
              memory: asset.memory,
              storage: asset.storage,
              networkInterface: asset.networkInterface,
              operatingSystem: asset.operatingSystem,
              ipAddress: asset.ipAddress,
              macAddress: asset.macAddress,
              portCount: asset.portCount,
              tags: []
            }

            unifiedDataService.addAsset(unifiedAsset)
            migratedCount++
          }

          // 更新进度
          this.updateMigrationProgress(module, i + 1, assets.length, 'running')

          // 模拟异步操作
          await new Promise((resolve) => setTimeout(resolve, 10))
        } catch (error: any) {
          errors.push(`迁移资产 ${asset.name} 失败: ${error.message}`)
        }
      }

      result.success = errors.length === 0
      result.migratedCount = migratedCount
      result.errors = errors

      // 完成迁移
      this.updateMigrationProgress(module, assets.length, assets.length, result.success ? 'completed' : 'failed')

      // 备份原数据
      if (result.success && migratedCount > 0) {
        this.backupOriginalData('asset_management_store', data)
      }
    } catch (error: any) {
      result.errors.push(`迁移过程出错: ${error.message}`)
      this.updateMigrationProgress(module, 0, 0, 'failed')
    }

    this.migrationHistory.push(result)
    this.notifyMigrationComplete(result)

    return result
  }

  // 辅助方法
  private mapAssetStatus(oldStatus: string): string {
    const statusMap: Record<string, string> = {
      active: 'online',
      maintenance: 'maintenance',
      idle: 'offline',
      retired: 'error'
    }
    return statusMap[oldStatus] || 'normal'
  }

  private updateMigrationProgress(module: string, current: number, total: number, status: MigrationProgress['status']): void {
    const progress: MigrationProgress = {
      module,
      current,
      total,
      percentage: total > 0 ? Math.round((current / total) * 100) : 0,
      status
    }

    this.currentMigrations.set(module, progress)

    // 发送进度更新事件
    eventBus.emit('migrationProgress', progress)
  }

  private backupOriginalData(type: string, data: any): void {
    const backupKey = `backup_${type}_${Date.now()}`
    try {
      localStorage.setItem(
        backupKey,
        JSON.stringify({
          ...data,
          backupTimestamp: new Date().toISOString(),
          backupReason: 'migration_to_unified_service'
        })
      )
      console.log(`原始数据已备份到: ${backupKey}`)
    } catch (error) {
      console.error('备份原始数据失败:', error)
    }
  }

  private notifyMigrationComplete(result: MigrationResult): void {
    eventBus.emit('migrationComplete', result)

    if (result.success) {
      console.log(`✅ ${result.module} 迁移成功: ${result.migratedCount} 条数据`)
    } else {
      console.error(`❌ ${result.module} 迁移失败:`, result.errors)
    }
  }

  /**
   * 获取迁移历史
   */
  getMigrationHistory(): MigrationResult[] {
    return [...this.migrationHistory]
  }

  /**
   * 获取当前迁移进度
   */
  getCurrentMigrations(): MigrationProgress[] {
    return Array.from(this.currentMigrations.values())
  }

  /**
   * 清除迁移历史
   */
  clearMigrationHistory(): void {
    this.migrationHistory = []
    this.currentMigrations.clear()
  }
}

export const dataMigrationService = new DataMigrationService()
export default dataMigrationService
