# 纠错行为规范 · 项目级独立规则文档

## 修复错误（bug fix）\*\* 过程中保护已完成模块的功能意图与结构完整性，防止无意删改导致功能退化或丢失。

## 1. 激活条件 · 何时启动该规则

只要 AI 工具应用户要求执行以下任意行为，即视为进入“纠错风险态”，**必须激活本规则并执行后续流程**：

- 修改已有模块以解决运行错误、类型错误、编译报错等问题；
- 删除、注释、简化原有核心逻辑，如功能判断、跳转、表单校验等；
- 使用默认返回值（如 return true/null）临时绕过逻辑；
- 更改函数的结构性路径、状态机流程、依赖注入、接口调用等关键链路。

---

## 2. 身份定义 · AI 在本流程中的权限与职责

AI 工具在本规则中角色为：**辅助执行者**，不具备独立结构性决策权。仅可在满足以下任意前提之一的条件下继续执行修复行为：

- 明确读取到模块意图文档，确认修改不会破坏既有结构；
- 修改内容已完整记录在 PATCHLOG 文件中；
- 如为结构性更改，用户已手动确认允许执行。
  若不满足以上任一条件，AI 工具需立即中止操作并发起用户提示。

---

## 3. 执行规则 · 所有行为必须遵循的硬性规范

### 3.1 读取功能意图说明（如存在）

若项目中存在功能说明文档（命名格式为：`INTENT_模块名.md`），AI 工具在执行任意修改前必须优先加载读取。
说明文档中应包含：

- 功能目标与模块边界
- 不可更改逻辑列表（如鉴权、跳转、状态控制等）
- 上下游依赖描述（如 API、Token、跳转路径）
  如当前修改行为与文档内容冲突（如将被禁止逻辑注释），AI 工具必须立即中止执行并提示用户。

### 3.2 记录补丁日志 PATCHLOG

每次修改必须记录到以当天命名的日志文件中：
文件名格式：

```
PATCHLOG_YYYY-MM-DD.md
```

记录格式：

```
### [模块路径]
- 修改位置：如 login/verify.ts > handleLogin()
- 修改前摘要：原代码行为简述
- 修改后摘要：修改后代码行为简述
- 修改原因：错误类型及定位依据
- 风险提示：该变更是否可能影响原功能
- 建议恢复方式：如问题解决后应如何还原逻辑
```

### 3.3 用户确认机制（结构性更改必须中断并提示）

若修改涉及以下结构性操作，必须中止并请求用户确认：

- 删除或注释整段逻辑，如函数体、流程判断分支；
- 使用 return true/null/undefined 等值绕过流程；
- 绕过鉴权、跳转、Token 存储、表单校验等关键逻辑；
- 更改状态管理、错误捕获结构、跳转路径等控制性逻辑。
  中断提示模板如下：

```
检测到本次修改为结构性更改，可能影响已实现的核心功能。
PATCHLOG_YYYY-MM-DD.md 已记录所有变更内容。
是否允许继续执行以下操作：
- 删除 router.push 登录跳转语句
- 注释 token 写入逻辑
请输入：「允许修改」或「取消修改」
```

---

## 4. 执行步骤 · AI 必须严格按序执行

1. **尝试读取 INTENT\_模块名.md**
   - 若存在：加载模块意图，校验是否允许当前修改；
   - 若不存在：跳过，但需标记“无意图说明文档”并继续执行下一步。
2. **记录修改前后差异至 PATCHLOG**
   - 补丁日志应每日归档，并具备 diff 概要与修改原因；
3. **若为结构性修改 → 中断请求用户确认**
   - 未获用户允许前不得继续执行该类型改动；
4. **获批后执行修复逻辑，并保留补丁记录**

---

## 5. 文档结构规范 · 文件命名统一模板

| 文档类型     | 命名格式示例           | 内容说明                               |
| ------------ | ---------------------- | -------------------------------------- |
| 功能意图说明 | INTENT_login-auth.md   | 说明模块目标、禁止改动逻辑、依赖结构   |
| 补丁修改日志 | PATCHLOG_2025-08-22.md | 记录每日修改摘要、差异、风险、建议恢复 |

---

## 6. 判定参考表 · 是否触发本规则

| 修改行为                                 | 是否触发本规则 |
| ---------------------------------------- | -------------- |
| 删除 router.push 跳转逻辑                | 是             |
| 注释 token 写入逻辑                      | 是             |
| 用 return true 替换完整权限校验逻辑      | 是             |
| 更改状态处理流程，使登录后不再进入欢迎页 | 是             |
| 修改 CSS 样式类名                        | 否             |
| 添加调试日志，不影响功能逻辑             | 否             |
| 调整响应报文字段格式但保留核心流程       | 否             |

---

## 7. 违规处理 · 严禁跳过本规则流程

如 AI 工具在修复错误时存在以下行为，视为违规操作：

- 未查阅 INTENT 功能说明文档；
- 未写入 PATCHLOG 文件；
- 未经用户确认擅自执行结构性更改；
  一经发现，必须立刻中止本次变更，并执行完整回滚或发起新的合规流程。此类行为可被记录为「违反规则性指令」。

# AI协作开发流程规范

## 📋 概述

本文档定义了Claude Code和Trae AI在开发此项目时必须遵循的协作流程和技术标准，确保代码质量和开发一致性。

## 🎯 协作原则

### 核心原则

1. **先检查，后开发** - 每次开发前必须检查现有代码状态
2. **记录驱动** - 所有修改都必须记录在案
3. **类型安全** - 严格遵循TypeScript类型定义
4. **接口统一** - 前后端接口必须保持一致
5. **测试优先** - 新功能必须包含测试

### 开发流程

```
1. 检查项目状态 → 2. 记录开发计划 → 3. 实施开发 → 4. 记录变更 → 5. 验证功能
```

## 📁 文档结构

```
docs/
├── AI-COLLABORATION-STANDARDS.md      # 本文档（协作流程规范）
├── TECHNICAL-STANDARDS.md             # 技术标准和代码规范
├── API-INTERFACE-SPECS.md              # API接口规范
├── dev-logs/                          # 开发记录目录
│   ├── claude-dev-log.md              # Claude的开发记录
│   ├── trae-dev-log.md                # Trae的开发记录
│   └── collaboration-issues.md         # 协作问题记录
└── templates/                         # 模板文件
    ├── dev-record-template.md         # 开发记录模板
    └── feature-checklist.md           # 功能开发检查清单
```

## 🔄 强制工作流程

### 开发前检查清单

- [ ] 检查`dev-logs/`中最新的开发记录
- [ ] 运行`npm run type-check`确认无类型错误
- [ ] 检查`docs/collaboration-issues.md`中的已知问题
- [ ] 确认当前分支状态和最新提交

### 开发过程要求

- [ ] 必须先在`dev-logs/`中记录开发计划
- [ ] 遵循`TECHNICAL-STANDARDS.md`中的编码规范
- [ ] 新增API必须在`API-INTERFACE-SPECS.md`中记录
- [ ] 类型定义必须添加到`shared/types.ts`

### 开发后验证

- [ ] 更新开发记录，说明实际完成的内容
- [ ] 运行类型检查和基本功能测试
- [ ] 记录任何发现的问题到`collaboration-issues.md`
- [ ] 更新相关文档

## 🚨 冲突解决机制

### 发现冲突时

1. **立即停止开发**
2. **记录冲突详情**到`collaboration-issues.md`
3. **分析冲突原因**（类型不匹配、接口变更等）
4. **制定解决方案**并记录
5. **实施修复**后继续开发

### 冲突预防

- 修改公共接口前必须先更新文档
- 新增依赖前必须检查兼容性
- 大型重构前必须制定详细计划

## 📝 记录要求

### 每次开发必须记录

1. **开发目的** - 解决什么问题或添加什么功能
2. **技术选择** - 使用的技术栈、依赖、架构决策
3. **接口变更** - API、类型定义、数据库schema的变更
4. **已知问题** - 发现但未解决的问题
5. **测试情况** - 功能测试结果和注意事项

### 记录格式

使用标准化的Markdown模板，包含时间戳、修改范围、详细说明等

## 🛠 技术约束

### 必须遵循的规范

- TypeScript严格模式
- 统一的错误处理格式
- 统一的API响应结构
- 统一的日志记录方式
- 统一的数据验证方式（使用zod）

### 禁止行为

- 绕过类型检查（使用any等）
- 未记录的接口修改
- 删除他人代码前未确认影响
- 添加未在package.json中声明的依赖

## 📊 质量保证

### 自动化检查

- TypeScript类型检查
- ESLint代码规范检查
- Prettier代码格式化
- 基本功能测试

### 手动检查

- 开发记录完整性
- 文档更新及时性
- 接口兼容性
- 功能集成测试

---

**重要提醒**: 每个AI都必须在开始任何开发工作前阅读此文档和相关技术标准，确保遵循所有流程要求。
**最后更新**: 2025-09-14 by Claude Code
**下次审查**: 根据协作过程中遇到的问题定期更新

# 开发记录模板

## **复制此模板到对应的开发记录文件中，填写具体信息**

## 📋 开发记录 - [日期]

### 🆔 记录信息

- **开发者**: [Claude Code / Trae AI]
- **日期**: YYYY-MM-DD
- **时间**: HH:MM - HH:MM
- **会话ID**: [可选，用于追踪具体会话]

### 🎯 开发目标

简要描述本次开发要解决的问题或实现的功能：

- [ ] 主要目标1
- [ ] 主要目标2
- [ ] 主要目标3

### 📊 项目状态检查 (开发前)

- **TypeScript编译状态**: ✅ 无错误 / ❌ 有错误
- **服务运行状态**: 前端 ✅/❌ | 后端 ✅/❌
- **已知问题**: [列出检查时发现的问题]
- **Git状态**: [当前分支，未提交的更改等]

### 🛠 技术选择和架构决策

记录本次开发中的重要技术选择：

#### 使用的技术栈

- **前端**: [React, TypeScript, Ant Design等]
- **后端**: [Express, Prisma, 等]
- **新增依赖**: [如果有新增的npm包]
- **AI服务**: [使用的AI模型和服务]

#### 架构决策

- **设计模式**: [使用的设计模式]
- **数据存储**: [数据库选择和结构变更]
- **API设计**: [新增或修改的API接口]

### 🔧 具体实现内容

#### 文件变更清单

记录所有创建、修改、删除的文件：
**新增文件**:

- `path/to/new-file.ts` - [文件作用说明]
- `path/to/another-file.tsx` - [文件作用说明]
  **修改文件**:
- `path/to/existing-file.ts` - [修改内容说明]
- `path/to/another-existing-file.tsx` - [修改内容说明]
  **删除文件**:
- `path/to/deleted-file.ts` - [删除原因]

#### 数据库变更

- **Schema变更**: [Prisma模型的修改]
- **迁移文件**: [新增的迁移文件名]
- **数据种子**: [测试数据的变更]

#### API变更

记录新增或修改的API接口：
**新增接口**:

- `POST /api/new-endpoint` - [接口作用]
- `GET /api/another-endpoint` - [接口作用]
  **修改接口**:
- `PUT /api/existing-endpoint` - [修改内容]
  **废弃接口**:
- `DELETE /api/old-endpoint` - [废弃原因]

### 🎨 前端变更 (如适用)

- **新增组件**: [列出新增的React组件]
- **页面修改**: [修改的页面和路由]
- **样式变更**: [CSS/样式的重要修改]
- **状态管理**: [Redux/Zustand状态的变更]

### 🔒 后端变更 (如适用)

- **控制器**: [新增或修改的控制器]
- **服务层**: [业务逻辑变更]
- **中间件**: [认证、权限等中间件变更]
- **工具函数**: [通用工具函数的变更]

### 📝 类型定义变更

记录TypeScript类型定义的变更：
**新增类型**:

```typescript
// 在shared/types.ts或其他文件中新增的类型
interface NewInterface {
  // 类型定义
}
```

**修改类型**:

```typescript
// 修改的现有类型
interface ModifiedInterface {
  // 修改说明
}
```

### 🧪 测试情况

- **功能测试**: [测试了哪些功能]
- **集成测试**: [前后端集成测试结果]
- **错误场景**: [测试的错误处理情况]
- **性能测试**: [如有性能相关测试]

### ⚠️ 已知问题和限制

列出开发过程中发现但未解决的问题：

1. **问题描述**: [具体问题]
   - **影响范围**: [影响哪些功能]
   - **临时解决方案**: [如果有的话]
   - **计划解决时间**: [预期何时解决]
2. **技术债务**: [需要重构或优化的代码]
   - **位置**: [具体文件和代码位置]
   - **原因**: [为什么产生技术债务]

### 📋 配置变更

- **环境变量**: [新增或修改的环境变量]
- **配置文件**: [修改的配置文件]
- **依赖更新**: [package.json的变更]

### 🔄 与其他AI的协作

- **读取的其他开发记录**: [查看了哪些其他AI的开发记录]
- **发现的冲突**: [是否发现与其他AI开发的冲突]
- **协调措施**: [采取了哪些协调措施]

### 📊 项目状态检查 (开发后)

- **TypeScript编译状态**: ✅ 无错误 / ❌ 有错误
- **服务运行状态**: 前端 ✅/❌ | 后端 ✅/❌
- **基本功能测试**: ✅ 通过 / ❌ 有问题
- **新问题**: [开发过程中新发现的问题]

### 🎯 下一步计划

- [ ] 需要跟进的任务1
- [ ] 需要跟进的任务2
- [ ] 建议其他AI关注的事项

### 📚 参考资源

- **文档**: [参考的技术文档]
- **代码示例**: [参考的代码或项目]
- **问题解决**: [查阅的Stack Overflow或其他资源]

### 💭 反思和改进

- **做得好的地方**: [本次开发中的亮点]
- **可以改进的地方**: [下次可以做得更好的方面]
- **学到的经验**: [技术或流程方面的收获]

---

**记录完成时间**: YYYY-MM-DD HH:MM:SS
**下次开发建议**: [给下一个开发者的建议]
